#!/bin/bash

# 🚀 Kontour Coin Professional Website Deployment Script
# Advanced deployment with optimization, security, and monitoring

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
WEBSITE_PORT=8000
DOMAIN="kontourcoin.com"
SSL_ENABLED=false
OPTIMIZATION_ENABLED=true
MONITORING_ENABLED=true

echo -e "${PURPLE}🚀 Kontour Coin Professional Website Deployment${NC}"
echo -e "${CYAN}================================================${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install dependencies
install_dependencies() {
    echo -e "${BLUE}📦 Installing dependencies...${NC}"
    
    # Check for Python
    if ! command_exists python3; then
        echo -e "${RED}❌ Python 3 is required but not installed${NC}"
        exit 1
    fi
    
    # Check for Node.js (optional for optimization)
    if ! command_exists node && [ "$OPTIMIZATION_ENABLED" = true ]; then
        echo -e "${YELLOW}⚠️  Node.js not found. Optimization features will be limited.${NC}"
        OPTIMIZATION_ENABLED=false
    fi
    
    # Install Python dependencies
    if [ ! -d "venv" ]; then
        echo -e "${BLUE}🐍 Creating Python virtual environment...${NC}"
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    pip install --upgrade pip
    pip install http.server socketserver watchdog
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Function to optimize assets
optimize_assets() {
    if [ "$OPTIMIZATION_ENABLED" = false ]; then
        return
    fi
    
    echo -e "${BLUE}⚡ Optimizing website assets...${NC}"
    
    # Create optimized directory
    mkdir -p optimized
    
    # Copy all files to optimized directory
    cp -r *.html styles scripts assets optimized/ 2>/dev/null || true
    
    # Minify CSS (basic minification)
    if command_exists node; then
        echo -e "${BLUE}   Minifying CSS files...${NC}"
        for css_file in optimized/styles/*.css; do
            if [ -f "$css_file" ]; then
                # Basic CSS minification (remove comments and extra whitespace)
                sed -e 's/\/\*.*\*\///g' -e 's/[[:space:]]\+/ /g' -e 's/; /;/g' -e 's/ {/{/g' -e 's/{ /{/g' -e 's/ }/}/g' "$css_file" > "${css_file}.min"
                mv "${css_file}.min" "$css_file"
            fi
        done
    fi
    
    # Optimize images (if imagemagick is available)
    if command_exists convert; then
        echo -e "${BLUE}   Optimizing images...${NC}"
        find optimized/assets -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | while read img; do
            convert "$img" -quality 85 -strip "$img"
        done
    fi
    
    echo -e "${GREEN}✅ Asset optimization completed${NC}"
}

# Function to generate SSL certificate (self-signed for development)
generate_ssl() {
    if [ "$SSL_ENABLED" = false ]; then
        return
    fi
    
    echo -e "${BLUE}🔒 Generating SSL certificate...${NC}"
    
    mkdir -p ssl
    
    # Generate self-signed certificate for development
    openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \
        -subj "/C=US/ST=CA/L=San Francisco/O=Kontour Coin/CN=$DOMAIN" 2>/dev/null
    
    echo -e "${GREEN}✅ SSL certificate generated${NC}"
    echo -e "${YELLOW}⚠️  Using self-signed certificate for development. Use proper SSL in production.${NC}"
}

# Function to create server configuration
create_server_config() {
    echo -e "${BLUE}⚙️  Creating server configuration...${NC}"
    
    cat > server.py << 'EOF'
#!/usr/bin/env python3
"""
Kontour Coin Professional Website Server
Advanced HTTP server with security headers, compression, and monitoring
"""

import http.server
import socketserver
import os
import gzip
import mimetypes
import json
import time
from urllib.parse import urlparse, parse_qs
from datetime import datetime

class KontourHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory="optimized" if os.path.exists("optimized") else ".", **kwargs)
    
    def end_headers(self):
        # Security headers
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        self.send_header('Referrer-Policy', 'strict-origin-when-cross-origin')
        self.send_header('Content-Security-Policy', "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:; img-src 'self' https: data:; font-src 'self' https: data:")
        
        # CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Cache headers
        if self.path.endswith(('.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico')):
            self.send_header('Cache-Control', 'public, max-age=31536000')  # 1 year
        else:
            self.send_header('Cache-Control', 'public, max-age=3600')  # 1 hour
        
        super().end_headers()
    
    def do_GET(self):
        # Log request
        self.log_request()
        
        # Handle API endpoints
        if self.path.startswith('/api/'):
            self.handle_api_request()
            return
        
        # Serve static files
        super().do_GET()
    
    def handle_api_request(self):
        """Handle API requests for demo purposes"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/health':
            self.send_api_response({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        elif parsed_path.path == '/api/price':
            # Mock price data
            import random
            self.send_api_response({
                'price': round(2.4567 + (random.random() - 0.5) * 0.1, 4),
                'change_24h': round((random.random() - 0.5) * 20, 2),
                'volume_24h': round(random.random() * 50000000 + 10000000, 0),
                'market_cap': round(random.random() * ********** + **********, 0),
                'timestamp': datetime.now().isoformat()
            })
        else:
            self.send_error(404, 'API endpoint not found')
    
    def send_api_response(self, data):
        """Send JSON API response"""
        response = json.dumps(data).encode('utf-8')
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response)))
        self.end_headers()
        self.wfile.write(response)
    
    def log_request(self, code='-', size='-'):
        """Enhanced request logging"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {self.address_string()} - {self.requestline} - {code}")

class ThreadedHTTPServer(socketserver.ThreadingMixIn, http.server.HTTPServer):
    """Handle requests in separate threads"""
    allow_reuse_address = True

def run_server(port=8000):
    """Run the Kontour Coin website server"""
    handler = KontourHTTPRequestHandler
    
    with ThreadedHTTPServer(("", port), handler) as httpd:
        print(f"🚀 Kontour Coin website running at http://localhost:{port}")
        print(f"📊 API endpoints available at http://localhost:{port}/api/")
        print("Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")

if __name__ == "__main__":
    import sys
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8000
    run_server(port)
EOF
    
    chmod +x server.py
    echo -e "${GREEN}✅ Server configuration created${NC}"
}

# Function to create monitoring script
create_monitoring() {
    if [ "$MONITORING_ENABLED" = false ]; then
        return
    fi
    
    echo -e "${BLUE}📊 Setting up monitoring...${NC}"
    
    cat > monitor.py << 'EOF'
#!/usr/bin/env python3
"""
Kontour Coin Website Monitor
Real-time monitoring and health checks
"""

import time
import requests
import psutil
import json
from datetime import datetime

class WebsiteMonitor:
    def __init__(self, url="http://localhost:8000"):
        self.url = url
        self.start_time = time.time()
    
    def check_health(self):
        """Check website health"""
        try:
            response = requests.get(f"{self.url}/api/health", timeout=5)
            return {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'response_time': response.elapsed.total_seconds(),
                'status_code': response.status_code
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'response_time': None,
                'status_code': None
            }
    
    def get_system_metrics(self):
        """Get system performance metrics"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'uptime': time.time() - self.start_time
        }
    
    def run_monitoring(self, interval=30):
        """Run continuous monitoring"""
        print("🔍 Starting website monitoring...")
        
        while True:
            try:
                health = self.check_health()
                metrics = self.get_system_metrics()
                
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                print(f"\n[{timestamp}] Website Health Check:")
                print(f"  Status: {health['status']}")
                if health['response_time']:
                    print(f"  Response Time: {health['response_time']:.3f}s")
                
                print(f"  System Metrics:")
                print(f"    CPU: {metrics['cpu_percent']:.1f}%")
                print(f"    Memory: {metrics['memory_percent']:.1f}%")
                print(f"    Disk: {metrics['disk_percent']:.1f}%")
                print(f"    Uptime: {metrics['uptime']:.0f}s")
                
                # Log to file
                with open('monitoring.log', 'a') as f:
                    log_entry = {
                        'timestamp': timestamp,
                        'health': health,
                        'metrics': metrics
                    }
                    f.write(json.dumps(log_entry) + '\n')
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                print("\n🛑 Monitoring stopped")
                break
            except Exception as e:
                print(f"❌ Monitoring error: {e}")
                time.sleep(interval)

if __name__ == "__main__":
    monitor = WebsiteMonitor()
    monitor.run_monitoring()
EOF
    
    chmod +x monitor.py
    echo -e "${GREEN}✅ Monitoring setup completed${NC}"
}

# Function to validate website
validate_website() {
    echo -e "${BLUE}🔍 Validating website...${NC}"
    
    # Check required files
    required_files=("index.html" "styles/advanced-professional.css" "scripts/advanced-main.js")
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo -e "${RED}❌ Required file missing: $file${NC}"
            exit 1
        fi
    done
    
    # Check HTML syntax (basic)
    for html_file in *.html; do
        if [ -f "$html_file" ]; then
            # Basic HTML validation
            if ! grep -q "<!DOCTYPE html>" "$html_file"; then
                echo -e "${YELLOW}⚠️  Missing DOCTYPE in $html_file${NC}"
            fi
            
            if ! grep -q "</html>" "$html_file"; then
                echo -e "${YELLOW}⚠️  Missing closing </html> tag in $html_file${NC}"
            fi
        fi
    done
    
    echo -e "${GREEN}✅ Website validation completed${NC}"
}

# Function to start the website
start_website() {
    echo -e "${BLUE}🚀 Starting Kontour Coin website...${NC}"
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Start the server
    echo -e "${GREEN}✅ Website starting on port $WEBSITE_PORT${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo -e "${BLUE}🌐 Website URLs:${NC}"
    echo -e "${GREEN}   • Main Site:      http://localhost:$WEBSITE_PORT${NC}"
    echo -e "${GREEN}   • Trading:        http://localhost:$WEBSITE_PORT/trading.html${NC}"
    echo -e "${GREEN}   • Wallet:         http://localhost:$WEBSITE_PORT/wallet.html${NC}"
    echo -e "${GREEN}   • AI Agents:      http://localhost:$WEBSITE_PORT/ai-agents.html${NC}"
    echo -e "${GREEN}   • Analytics:      http://localhost:$WEBSITE_PORT/analytics.html${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo -e "${BLUE}📊 API Endpoints:${NC}"
    echo -e "${GREEN}   • Health Check:   http://localhost:$WEBSITE_PORT/api/health${NC}"
    echo -e "${GREEN}   • Price Data:     http://localhost:$WEBSITE_PORT/api/price${NC}"
    echo -e "${CYAN}================================================${NC}"
    
    # Start monitoring in background if enabled
    if [ "$MONITORING_ENABLED" = true ]; then
        echo -e "${BLUE}📊 Starting monitoring...${NC}"
        python3 monitor.py &
        MONITOR_PID=$!
        echo $MONITOR_PID > monitor.pid
    fi
    
    # Start the main server
    python3 server.py $WEBSITE_PORT
}

# Function to stop services
stop_services() {
    echo -e "\n${YELLOW}🛑 Stopping services...${NC}"
    
    # Stop monitoring
    if [ -f "monitor.pid" ]; then
        MONITOR_PID=$(cat monitor.pid)
        if kill -0 $MONITOR_PID 2>/dev/null; then
            kill $MONITOR_PID
            echo -e "${GREEN}✅ Monitoring stopped${NC}"
        fi
        rm -f monitor.pid
    fi
    
    echo -e "${GREEN}✅ All services stopped${NC}"
    exit 0
}

# Set up signal handlers
trap stop_services SIGINT SIGTERM

# Main execution
main() {
    echo -e "${BLUE}🔧 Initializing deployment...${NC}"
    
    # Install dependencies
    install_dependencies
    
    # Validate website
    validate_website
    
    # Optimize assets
    optimize_assets
    
    # Generate SSL if enabled
    generate_ssl
    
    # Create server configuration
    create_server_config
    
    # Create monitoring
    create_monitoring
    
    echo -e "\n${GREEN}🎉 Deployment preparation completed!${NC}"
    echo -e "${PURPLE}💎 Kontour Coin Professional Website Ready${NC}"
    
    # Start the website
    start_website
}

# Run main function
main "$@"
