const express = require('express');
const cors = require('cors');
const WebSocket = require('ws');
const { createServer } = require('http');

const app = express();
const server = createServer(app);
const wss = new WebSocket.Server({ server });

// Middleware
app.use(cors());
app.use(express.json());

// Exchange Configuration
const EXCHANGES = {
    binance: {
        name: 'Binance',
        api_url: 'https://api.binance.com',
        websocket_url: 'wss://stream.binance.com:9443',
        supported_pairs: ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'SOL/USDT', 'ADA/USDT', 'DOT/USDT'],
        fees: { maker: 0.001, taker: 0.001 },
        status: 'active'
    },
    coinbase: {
        name: 'Coinbase Pro',
        api_url: 'https://api.pro.coinbase.com',
        websocket_url: 'wss://ws-feed.pro.coinbase.com',
        supported_pairs: ['BTC/USD', 'ETH/USD', 'KONTOUR/USD'],
        fees: { maker: 0.005, taker: 0.005 },
        status: 'active'
    },
    kraken: {
        name: 'Kraken',
        api_url: 'https://api.kraken.com',
        websocket_url: 'wss://ws.kraken.com',
        supported_pairs: ['BTC/USD', 'ETH/USD', 'SOL/USD', 'ADA/USD', 'DOT/USD'],
        fees: { maker: 0.0016, taker: 0.0026 },
        status: 'active'
    },
    kucoin: {
        name: 'KuCoin',
        api_url: 'https://api.kucoin.com',
        websocket_url: 'wss://ws-api.kucoin.com',
        supported_pairs: ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'SOL/USDT'],
        fees: { maker: 0.001, taker: 0.001 },
        status: 'active'
    },
    huobi: {
        name: 'Huobi',
        api_url: 'https://api.huobi.pro',
        websocket_url: 'wss://api.huobi.pro/ws',
        supported_pairs: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'ADA/USDT'],
        fees: { maker: 0.002, taker: 0.002 },
        status: 'active'
    },
    okx: {
        name: 'OKX',
        api_url: 'https://www.okx.com/api',
        websocket_url: 'wss://ws.okx.com:8443/ws/v5/public',
        supported_pairs: ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'DOT/USDT'],
        fees: { maker: 0.0008, taker: 0.001 },
        status: 'active'
    },
    bybit: {
        name: 'Bybit',
        api_url: 'https://api.bybit.com',
        websocket_url: 'wss://stream.bybit.com/v5/public/spot',
        supported_pairs: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],
        fees: { maker: 0.001, taker: 0.001 },
        status: 'active'
    },
    gate: {
        name: 'Gate.io',
        api_url: 'https://api.gateio.ws',
        websocket_url: 'wss://api.gateio.ws/ws/v4/',
        supported_pairs: ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'ADA/USDT'],
        fees: { maker: 0.002, taker: 0.002 },
        status: 'active'
    }
};

// Market Data Storage
const marketData = new Map();
const orderBooks = new Map();
const tradeHistory = new Map();
const exchangeConnections = new Map();

// AI Agent Integration
const agentOrders = new Map();
const agentPositions = new Map();
const arbitrageOpportunities = new Map();

// Initialize Market Data
function initializeMarketData() {
    const pairs = ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'SOL/USDT', 'ADA/USDT', 'DOT/USDT', 'USDC/USDT'];
    
    pairs.forEach(pair => {
        const basePrice = getBasePriceForPair(pair);
        
        Object.keys(EXCHANGES).forEach(exchange => {
            const key = `${exchange}:${pair}`;
            marketData.set(key, {
                exchange,
                pair,
                price: basePrice * (0.98 + Math.random() * 0.04), // ±2% variance
                volume_24h: Math.random() * 1000000,
                change_24h: (Math.random() - 0.5) * 0.1, // ±5%
                bid: basePrice * (0.999 + Math.random() * 0.001),
                ask: basePrice * (1.001 + Math.random() * 0.001),
                timestamp: Date.now(),
                status: 'active'
            });
        });
    });
}

function getBasePriceForPair(pair) {
    const basePrices = {
        'BTC/USDT': 45000,
        'ETH/USDT': 3200,
        'KONTOUR/USDT': 1.25,
        'SOL/USDT': 110,
        'ADA/USDT': 0.45,
        'DOT/USDT': 7.5,
        'USDC/USDT': 1.0
    };
    return basePrices[pair] || 100;
}

// Exchange API Functions
async function getExchangePrices(exchange, pair) {
    try {
        const key = `${exchange}:${pair}`;
        const data = marketData.get(key);
        
        if (!data) {
            throw new Error(`No data available for ${pair} on ${exchange}`);
        }
        
        // Simulate real-time price updates
        const priceVariation = (Math.random() - 0.5) * 0.002; // ±0.1%
        data.price *= (1 + priceVariation);
        data.timestamp = Date.now();
        
        marketData.set(key, data);
        
        return {
            success: true,
            exchange,
            pair,
            price: data.price,
            bid: data.bid,
            ask: data.ask,
            volume: data.volume_24h,
            change_24h: data.change_24h,
            timestamp: data.timestamp
        };
        
    } catch (error) {
        return {
            success: false,
            error: error.message,
            exchange,
            pair
        };
    }
}

async function placeOrder(exchange, pair, side, amount, price, agentId = null) {
    try {
        const exchangeConfig = EXCHANGES[exchange];
        if (!exchangeConfig) {
            throw new Error(`Exchange ${exchange} not supported`);
        }
        
        const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const fee = amount * price * (side === 'buy' ? exchangeConfig.fees.taker : exchangeConfig.fees.maker);
        
        const order = {
            id: orderId,
            exchange,
            pair,
            side,
            amount,
            price,
            fee,
            status: 'filled', // Simulate immediate fill for demo
            filled_amount: amount,
            remaining_amount: 0,
            created_at: Date.now(),
            filled_at: Date.now(),
            agent_id: agentId
        };
        
        // Store order for agent tracking
        if (agentId) {
            const agentOrderList = agentOrders.get(agentId) || [];
            agentOrderList.push(order);
            agentOrders.set(agentId, agentOrderList);
            
            // Update agent positions
            updateAgentPosition(agentId, pair, side, amount, price);
        }
        
        // Add to trade history
        const tradeKey = `${exchange}:${pair}`;
        const trades = tradeHistory.get(tradeKey) || [];
        trades.push({
            price,
            amount,
            side,
            timestamp: Date.now(),
            order_id: orderId
        });
        tradeHistory.set(tradeKey, trades.slice(-100)); // Keep last 100 trades
        
        return {
            success: true,
            order,
            message: `Order placed successfully on ${exchange}`
        };
        
    } catch (error) {
        return {
            success: false,
            error: error.message,
            exchange,
            pair
        };
    }
}

function updateAgentPosition(agentId, pair, side, amount, price) {
    const positions = agentPositions.get(agentId) || {};
    
    if (!positions[pair]) {
        positions[pair] = {
            amount: 0,
            avg_price: 0,
            total_cost: 0,
            unrealized_pnl: 0
        };
    }
    
    const position = positions[pair];
    
    if (side === 'buy') {
        const newTotalCost = position.total_cost + (amount * price);
        const newAmount = position.amount + amount;
        position.avg_price = newTotalCost / newAmount;
        position.amount = newAmount;
        position.total_cost = newTotalCost;
    } else {
        position.amount -= amount;
        if (position.amount <= 0) {
            position.amount = 0;
            position.avg_price = 0;
            position.total_cost = 0;
        }
    }
    
    // Calculate unrealized P&L
    const currentPrice = getCurrentPrice(pair);
    position.unrealized_pnl = (currentPrice - position.avg_price) * position.amount;
    
    positions[pair] = position;
    agentPositions.set(agentId, positions);
}

function getCurrentPrice(pair) {
    // Get average price across exchanges
    const prices = [];
    Object.keys(EXCHANGES).forEach(exchange => {
        const key = `${exchange}:${pair}`;
        const data = marketData.get(key);
        if (data) prices.push(data.price);
    });
    
    return prices.length > 0 ? prices.reduce((a, b) => a + b) / prices.length : 0;
}

// Arbitrage Detection
function detectArbitrageOpportunities() {
    const opportunities = [];
    const pairs = ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'SOL/USDT'];
    
    pairs.forEach(pair => {
        const exchangePrices = [];
        
        Object.keys(EXCHANGES).forEach(exchange => {
            const key = `${exchange}:${pair}`;
            const data = marketData.get(key);
            if (data) {
                exchangePrices.push({
                    exchange,
                    price: data.price,
                    bid: data.bid,
                    ask: data.ask
                });
            }
        });
        
        // Find arbitrage opportunities
        if (exchangePrices.length >= 2) {
            exchangePrices.sort((a, b) => a.price - b.price);
            const lowest = exchangePrices[0];
            const highest = exchangePrices[exchangePrices.length - 1];
            
            const priceDiff = highest.price - lowest.price;
            const profitPercent = (priceDiff / lowest.price) * 100;
            
            if (profitPercent > 0.1) { // Minimum 0.1% profit
                opportunities.push({
                    pair,
                    buy_exchange: lowest.exchange,
                    sell_exchange: highest.exchange,
                    buy_price: lowest.price,
                    sell_price: highest.price,
                    profit_percent: profitPercent,
                    profit_amount: priceDiff,
                    timestamp: Date.now()
                });
            }
        }
    });
    
    return opportunities;
}

// API Endpoints
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'exchange-integration',
        exchanges_connected: Object.keys(EXCHANGES).length,
        timestamp: new Date().toISOString()
    });
});

app.get('/api/exchanges', (req, res) => {
    res.json({
        success: true,
        exchanges: Object.entries(EXCHANGES).map(([id, config]) => ({
            id,
            name: config.name,
            status: config.status,
            supported_pairs: config.supported_pairs,
            fees: config.fees
        }))
    });
});

app.get('/api/exchanges/:exchange/prices/:pair', async (req, res) => {
    try {
        const { exchange, pair } = req.params;
        const result = await getExchangePrices(exchange, pair);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/market-data/:pair', (req, res) => {
    try {
        const { pair } = req.params;
        const exchangeData = [];
        
        Object.keys(EXCHANGES).forEach(exchange => {
            const key = `${exchange}:${pair}`;
            const data = marketData.get(key);
            if (data) {
                exchangeData.push({
                    exchange,
                    ...data
                });
            }
        });
        
        res.json({
            success: true,
            pair,
            exchanges: exchangeData,
            average_price: exchangeData.reduce((sum, d) => sum + d.price, 0) / exchangeData.length
        });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.post('/api/orders', async (req, res) => {
    try {
        const { exchange, pair, side, amount, price, agent_id } = req.body;
        
        if (!exchange || !pair || !side || !amount) {
            return res.status(400).json({ error: 'Missing required parameters' });
        }
        
        const result = await placeOrder(exchange, pair, side, amount, price, agent_id);
        res.json(result);
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/agents/:agentId/orders', (req, res) => {
    try {
        const { agentId } = req.params;
        const orders = agentOrders.get(agentId) || [];
        
        res.json({
            success: true,
            agent_id: agentId,
            orders,
            total_orders: orders.length
        });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/agents/:agentId/positions', (req, res) => {
    try {
        const { agentId } = req.params;
        const positions = agentPositions.get(agentId) || {};
        
        res.json({
            success: true,
            agent_id: agentId,
            positions
        });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/arbitrage', (req, res) => {
    try {
        const opportunities = detectArbitrageOpportunities();
        
        res.json({
            success: true,
            opportunities,
            count: opportunities.length,
            timestamp: Date.now()
        });
        
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// WebSocket for real-time market data
wss.on('connection', (ws) => {
    console.log('Exchange WebSocket client connected');
    
    // Send initial market data
    ws.send(JSON.stringify({
        type: 'market_data_snapshot',
        data: Array.from(marketData.entries()).map(([key, data]) => ({
            key,
            ...data
        }))
    }));
    
    ws.on('message', (data) => {
        try {
            const request = JSON.parse(data);
            const { type, payload } = request;
            
            if (type === 'subscribe_market_data') {
                // Handle market data subscription
                ws.send(JSON.stringify({
                    type: 'subscription_confirmed',
                    payload: { pairs: payload.pairs }
                }));
            }
            
        } catch (error) {
            console.error('WebSocket Exchange Error:', error);
        }
    });
    
    ws.on('close', () => {
        console.log('Exchange WebSocket client disconnected');
    });
});

// Real-time market data updates
setInterval(() => {
    initializeMarketData(); // Update prices
    
    // Broadcast to WebSocket clients
    const marketSnapshot = Array.from(marketData.entries()).map(([key, data]) => ({
        key,
        ...data
    }));
    
    wss.clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                type: 'market_data_update',
                data: marketSnapshot,
                timestamp: Date.now()
            }));
        }
    });
}, 5000); // Update every 5 seconds

// Initialize
initializeMarketData();

const PORT = process.env.PORT || 8002;
server.listen(PORT, () => {
    console.log(`🏦 Exchange Integration Service running on port ${PORT}`);
    console.log(`📊 Connected to ${Object.keys(EXCHANGES).length} exchanges`);
    console.log(`💱 Market data initialized for ${marketData.size} trading pairs`);
});

module.exports = app;
