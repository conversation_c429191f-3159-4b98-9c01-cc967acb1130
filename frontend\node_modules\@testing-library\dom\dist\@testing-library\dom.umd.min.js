!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryDom={})}(this,(function(e){"use strict";function t(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){if(e.__esModule)return e;var t=Object.defineProperty({},"__esModule",{value:!0});return Object.keys(e).forEach((function(r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})})),t}var o={},a={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>"["+(38+e)+";5;"+t+"m"},r=function(e){return void 0===e&&(e=0),(t,r,n)=>"["+(38+e)+";2;"+t+";"+r+";"+n+"m"};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,o]of Object.entries(r))n[t]={open:"["+o[0]+"m",close:"["+o[1]+"m"},r[t]=n[t],e.set(o[0],o[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(a);var i={};Object.defineProperty(i,"__esModule",{value:!0}),i.printIteratorEntries=function(e,t,r,n,o,a,i){void 0===i&&(i=": ");let l="",u=e.next();if(!u.done){l+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){l+=s+a(u.value[0],t,s,n,o)+i+a(u.value[1],t,s,n,o),u=e.next(),u.done?t.min||(l+=","):l+=","+t.spacingInner}l+=t.spacingOuter+r}return l},i.printIteratorValues=function(e,t,r,n,o,a){let i="",l=e.next();if(!l.done){i+=t.spacingOuter;const u=r+t.indent;for(;!l.done;)i+=u+a(l.value,t,u,n,o),l=e.next(),l.done?t.min||(i+=","):i+=","+t.spacingInner;i+=t.spacingOuter+r}return i},i.printListItems=function(e,t,r,n,o,a){let i="";if(e.length){i+=t.spacingOuter;const l=r+t.indent;for(let r=0;r<e.length;r++)i+=l,r in e&&(i+=a(e[r],t,l,n,o)),r<e.length-1?i+=","+t.spacingInner:t.min||(i+=",");i+=t.spacingOuter+r}return i},i.printObjectProperties=function(e,t,r,n,o,a){let i="";const u=l(e,t.compareKeys);if(u.length){i+=t.spacingOuter;const l=r+t.indent;for(let r=0;r<u.length;r++){const s=u[r];i+=l+a(s,t,l,n,o)+": "+a(e[s],t,l,n,o),r<u.length-1?i+=","+t.spacingInner:t.min||(i+=",")}i+=t.spacingOuter+r}return i};const l=(e,t)=>{const r=Object.keys(e).sort(t);return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)})),r};var u={};Object.defineProperty(u,"__esModule",{value:!0}),u.test=u.serialize=u.default=void 0;var s=i,c="undefined"!=typeof globalThis?globalThis:void 0!==c?c:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),d=c["jest-symbol-do-not-touch"]||c.Symbol;const p="function"==typeof d&&d.for?d.for("jest.asymmetricMatcher"):1267621,f=" ",m=(e,t,r,n,o,a)=>{const i=e.toString();return"ArrayContaining"===i||"ArrayNotContaining"===i?++n>t.maxDepth?"["+i+"]":i+f+"["+(0,s.printListItems)(e.sample,t,r,n,o,a)+"]":"ObjectContaining"===i||"ObjectNotContaining"===i?++n>t.maxDepth?"["+i+"]":i+f+"{"+(0,s.printObjectProperties)(e.sample,t,r,n,o,a)+"}":"StringMatching"===i||"StringNotMatching"===i||"StringContaining"===i||"StringNotContaining"===i?i+f+a(e.sample,t,r,n,o):e.toAsymmetricMatcher()};u.serialize=m;const b=e=>e&&e.$$typeof===p;u.test=b;var y={serialize:m,test:b};u.default=y;var v={};Object.defineProperty(v,"__esModule",{value:!0}),v.test=v.serialize=v.default=void 0;var h=P((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),g=P(a.exports);function P(e){return e&&e.__esModule?e:{default:e}}const C=e=>"string"==typeof e&&!!e.match((0,h.default)());v.test=C;const q=(e,t,r,n,o,a)=>a(e.replace((0,h.default)(),(e=>{switch(e){case g.default.red.close:case g.default.green.close:case g.default.cyan.close:case g.default.gray.close:case g.default.white.close:case g.default.yellow.close:case g.default.bgRed.close:case g.default.bgGreen.close:case g.default.bgYellow.close:case g.default.inverse.close:case g.default.dim.close:case g.default.bold.close:case g.default.reset.open:case g.default.reset.close:return"</>";case g.default.red.open:return"<red>";case g.default.green.open:return"<green>";case g.default.cyan.open:return"<cyan>";case g.default.gray.open:return"<gray>";case g.default.white.open:return"<white>";case g.default.yellow.open:return"<yellow>";case g.default.bgRed.open:return"<bgRed>";case g.default.bgGreen.open:return"<bgGreen>";case g.default.bgYellow.open:return"<bgYellow>";case g.default.inverse.open:return"<inverse>";case g.default.dim.open:return"<dim>";case g.default.bold.open:return"<bold>";default:return""}})),t,r,n,o);v.serialize=q;var w={serialize:q,test:C};v.default=w;var E={};Object.defineProperty(E,"__esModule",{value:!0}),E.test=E.serialize=E.default=void 0;var x=i;const O=["DOMStringMap","NamedNodeMap"],j=/^(HTML\w*Collection|NodeList)$/,S=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==O.indexOf(t)||j.test(t));var t};E.test=S;const R=(e,t,r,n,o,a)=>{const i=e.constructor.name;return++n>t.maxDepth?"["+i+"]":(t.min?"":i+" ")+(-1!==O.indexOf(i)?"{"+(0,x.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,o,a)+"}":"["+(0,x.printListItems)(Array.from(e),t,r,n,o,a)+"]")};E.serialize=R;var A={serialize:R,test:S};E.default=A;var T={},_={},M={};Object.defineProperty(M,"__esModule",{value:!0}),M.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty(_,"__esModule",{value:!0}),_.printText=_.printProps=_.printElementAsLeaf=_.printElement=_.printComment=_.printChildren=void 0;var I,B=(I=M)&&I.__esModule?I:{default:I};_.printProps=(e,t,r,n,o,a,i)=>{const l=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let c=i(s,r,l,o,a);return"string"!=typeof s&&(-1!==c.indexOf("\n")&&(c=r.spacingOuter+l+c+r.spacingOuter+n),c="{"+c+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+c+u.value.close})).join("")};_.printChildren=(e,t,r,n,o,a)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?F(e,t):a(e,t,r,n,o)))).join("");const F=(e,t)=>{const r=t.colors.content;return r.open+(0,B.default)(e)+r.close};_.printText=F;_.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,B.default)(e)+"--\x3e"+r.close};_.printElement=(e,t,r,n,o)=>{const a=n.colors.tag;return a.open+"<"+e+(t&&a.close+t+n.spacingOuter+o+a.open)+(r?">"+a.close+r+n.spacingOuter+o+a.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+a.close};_.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty(T,"__esModule",{value:!0}),T.test=T.serialize=T.default=void 0;var k=_;const N=/^((HTML|SVG)\w*)?Element$/,L=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,o="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&(N.test(t)||o)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function U(e){return 11===e.nodeType}T.test=L;const D=(e,t,r,n,o,a)=>{if(function(e){return 3===e.nodeType}(e))return(0,k.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,k.printComment)(e.data,t);const i=U(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,k.printElementAsLeaf)(i,t):(0,k.printElement)(i,(0,k.printProps)(U(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),U(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,o,a),(0,k.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,o,a),t,r)};T.serialize=D;var H={serialize:D,test:L};T.default=H;var z={};Object.defineProperty(z,"__esModule",{value:!0}),z.test=z.serialize=z.default=void 0;var W=i;const $="@@__IMMUTABLE_ORDERED__@@",V=e=>"Immutable."+e,G=e=>"["+e+"]",J=" ";const Q=(e,t,r,n,o,a,i)=>++n>t.maxDepth?G(V(i)):V(i)+J+"["+(0,W.printIteratorValues)(e.values(),t,r,n,o,a)+"]",X=(e,t,r,n,o,a)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,o,a,i)=>++n>t.maxDepth?G(V(i)):V(i)+J+"{"+(0,W.printIteratorEntries)(e.entries(),t,r,n,o,a)+"}")(e,t,r,n,o,a,e[$]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?Q(e,t,r,n,o,a,"List"):e["@@__IMMUTABLE_SET__@@"]?Q(e,t,r,n,o,a,e[$]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?Q(e,t,r,n,o,a,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,o,a)=>{const i=V("Seq");return++n>t.maxDepth?G(i):e["@@__IMMUTABLE_KEYED__@@"]?i+J+"{"+(e._iter||e._object?(0,W.printIteratorEntries)(e.entries(),t,r,n,o,a):"…")+"}":i+J+"["+(e._iter||e._array||e._collection||e._iterable?(0,W.printIteratorValues)(e.values(),t,r,n,o,a):"…")+"]"})(e,t,r,n,o,a):((e,t,r,n,o,a)=>{const i=V(e._name||"Record");return++n>t.maxDepth?G(i):i+J+"{"+(0,W.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,o,a)+"}"})(e,t,r,n,o,a);z.serialize=X;const K=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);z.test=K;var Y={serialize:X,test:K};z.default=Y;var Z={},ee={exports:{}},te={},re=60103,ne=60106,oe=60107,ae=60108,ie=60114,le=60109,ue=60110,se=60112,ce=60113,de=60120,pe=60115,fe=60116,me=60121,be=60122,ye=60117,ve=60129,he=60131;if("function"==typeof Symbol&&Symbol.for){var ge=Symbol.for;re=ge("react.element"),ne=ge("react.portal"),oe=ge("react.fragment"),ae=ge("react.strict_mode"),ie=ge("react.profiler"),le=ge("react.provider"),ue=ge("react.context"),se=ge("react.forward_ref"),ce=ge("react.suspense"),de=ge("react.suspense_list"),pe=ge("react.memo"),fe=ge("react.lazy"),me=ge("react.block"),be=ge("react.server.block"),ye=ge("react.fundamental"),ve=ge("react.debug_trace_mode"),he=ge("react.legacy_hidden")}function Pe(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case re:switch(e=e.type){case oe:case ie:case ae:case ce:case de:return e;default:switch(e=e&&e.$$typeof){case ue:case se:case fe:case pe:case le:return e;default:return t}}case ne:return t}}}var Ce=le,qe=re,we=se,Ee=oe,xe=fe,Oe=pe,je=ne,Se=ie,Re=ae,Ae=ce;te.ContextConsumer=ue,te.ContextProvider=Ce,te.Element=qe,te.ForwardRef=we,te.Fragment=Ee,te.Lazy=xe,te.Memo=Oe,te.Portal=je,te.Profiler=Se,te.StrictMode=Re,te.Suspense=Ae,te.isAsyncMode=function(){return!1},te.isConcurrentMode=function(){return!1},te.isContextConsumer=function(e){return Pe(e)===ue},te.isContextProvider=function(e){return Pe(e)===le},te.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===re},te.isForwardRef=function(e){return Pe(e)===se},te.isFragment=function(e){return Pe(e)===oe},te.isLazy=function(e){return Pe(e)===fe},te.isMemo=function(e){return Pe(e)===pe},te.isPortal=function(e){return Pe(e)===ne},te.isProfiler=function(e){return Pe(e)===ie},te.isStrictMode=function(e){return Pe(e)===ae},te.isSuspense=function(e){return Pe(e)===ce},te.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===oe||e===ie||e===ve||e===ae||e===ce||e===de||e===he||"object"==typeof e&&null!==e&&(e.$$typeof===fe||e.$$typeof===pe||e.$$typeof===le||e.$$typeof===ue||e.$$typeof===se||e.$$typeof===ye||e.$$typeof===me||e[0]===be)},te.typeOf=Pe,ee.exports=te,Object.defineProperty(Z,"__esModule",{value:!0}),Z.test=Z.serialize=Z.default=void 0;var Te=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=Me(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}n.default=e,r&&r.set(e,n);return n}(ee.exports),_e=_;function Me(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(Me=function(e){return e?r:t})(e)}const Ie=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{Ie(e,t)})):null!=e&&!1!==e&&t.push(e),t},Be=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(Te.isFragment(e))return"React.Fragment";if(Te.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(Te.isContextProvider(e))return"Context.Provider";if(Te.isContextConsumer(e))return"Context.Consumer";if(Te.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(Te.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},Fe=(e,t,r,n,o,a)=>++n>t.maxDepth?(0,_e.printElementAsLeaf)(Be(e),t):(0,_e.printElement)(Be(e),(0,_e.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,o,a),(0,_e.printChildren)(Ie(e.props.children),t,r+t.indent,n,o,a),t,r);Z.serialize=Fe;const ke=e=>null!=e&&Te.isElement(e);Z.test=ke;var Ne={serialize:Fe,test:ke};Z.default=Ne;var Le={};Object.defineProperty(Le,"__esModule",{value:!0}),Le.test=Le.serialize=Le.default=void 0;var Ue=_,De="undefined"!=typeof globalThis?globalThis:void 0!==De?De:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),He=De["jest-symbol-do-not-touch"]||De.Symbol;const ze="function"==typeof He&&He.for?He.for("react.test.json"):245830487,We=(e,t,r,n,o,a)=>++n>t.maxDepth?(0,Ue.printElementAsLeaf)(e.type,t):(0,Ue.printElement)(e.type,e.props?(0,Ue.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,o,a):"",e.children?(0,Ue.printChildren)(e.children,t,r+t.indent,n,o,a):"",t,r);Le.serialize=We;const $e=e=>e&&e.$$typeof===ze;Le.test=$e;var Ve={serialize:We,test:$e};Le.default=Ve,Object.defineProperty(o,"__esModule",{value:!0});var Ge=o.default=o.DEFAULT_OPTIONS=void 0,Je=o.format=_t,Qe=o.plugins=void 0,Xe=at(a.exports),Ke=i,Ye=at(u),Ze=at(v),et=at(E),tt=at(T),rt=at(z),nt=at(Z),ot=at(Le);function at(e){return e&&e.__esModule?e:{default:e}}const it=Object.prototype.toString,lt=Date.prototype.toISOString,ut=Error.prototype.toString,st=RegExp.prototype.toString,ct=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",dt=e=>"undefined"!=typeof window&&e===window,pt=/^Symbol\((.*)\)(.*)$/,ft=/\n/gi;class mt extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function bt(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function yt(e){return String(e).replace(pt,"Symbol($1)")}function vt(e){return"["+ut.call(e)+"]"}function ht(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const o=typeof e;if("number"===o)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===o)return function(e){return String(e+"n")}(e);if("string"===o)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===o)return bt(e,t);if("symbol"===o)return yt(e);const a=it.call(e);return"[object WeakMap]"===a?"WeakMap {}":"[object WeakSet]"===a?"WeakSet {}":"[object Function]"===a||"[object GeneratorFunction]"===a?bt(e,t):"[object Symbol]"===a?yt(e):"[object Date]"===a?isNaN(+e)?"Date { NaN }":lt.call(e):"[object Error]"===a?vt(e):"[object RegExp]"===a?r?st.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):st.call(e):e instanceof Error?vt(e):null}function gt(e,t,r,n,o,a){if(-1!==o.indexOf(e))return"[Circular]";(o=o.slice()).push(e);const i=++n>t.maxDepth,l=t.min;if(t.callToJSON&&!i&&e.toJSON&&"function"==typeof e.toJSON&&!a)return qt(e.toJSON(),t,r,n,o,!0);const u=it.call(e);return"[object Arguments]"===u?i?"[Arguments]":(l?"":"Arguments ")+"["+(0,Ke.printListItems)(e,t,r,n,o,qt)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?i?"["+e.constructor.name+"]":(l?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,Ke.printListItems)(e,t,r,n,o,qt)+"]":"[object Map]"===u?i?"[Map]":"Map {"+(0,Ke.printIteratorEntries)(e.entries(),t,r,n,o,qt," => ")+"}":"[object Set]"===u?i?"[Set]":"Set {"+(0,Ke.printIteratorValues)(e.values(),t,r,n,o,qt)+"}":i||dt(e)?"["+ct(e)+"]":(l?"":t.printBasicPrototype||"Object"!==ct(e)?ct(e)+" ":"")+"{"+(0,Ke.printObjectProperties)(e,t,r,n,o,qt)+"}"}function Pt(e,t,r,n,o,a){let i;try{i=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,o,a,qt):e.print(t,(e=>qt(e,r,n,o,a)),(e=>{const t=n+r.indent;return t+e.replace(ft,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new mt(e.message,e.stack)}if("string"!=typeof i)throw new Error('pretty-format: Plugin must return type "string" but instead returned "'+typeof i+'".');return i}function Ct(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new mt(e.message,e.stack)}return null}function qt(e,t,r,n,o,a){const i=Ct(t.plugins,e);if(null!==i)return Pt(i,e,t,r,n,o);const l=ht(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==l?l:gt(e,t,r,n,o,a)}const wt={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},Et=Object.keys(wt),xt={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:wt};var Ot=o.DEFAULT_OPTIONS=xt;const jt=e=>Et.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:wt[r],o=n&&Xe.default[n];if(!o||"string"!=typeof o.close||"string"!=typeof o.open)throw new Error('pretty-format: Option "theme" has a key "'+r+'" whose value "'+n+'" is undefined in ansi-styles.');return t[r]=o,t}),Object.create(null)),St=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:xt.printFunctionName,Rt=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:xt.escapeRegex,At=e=>e&&void 0!==e.escapeString?e.escapeString:xt.escapeString,Tt=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:xt.callToJSON,colors:e&&e.highlight?jt(e):Et.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:xt.compareKeys,escapeRegex:Rt(e),escapeString:At(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:xt.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:xt.maxDepth,min:e&&void 0!==e.min?e.min:xt.min,plugins:e&&void 0!==e.plugins?e.plugins:xt.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:St(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function _t(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!xt.hasOwnProperty(e))throw new Error('pretty-format: Unknown option "'+e+'".')})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error('pretty-format: Option "theme" must be of type "object" but instead received "'+typeof e.theme+'".')}}(t),t.plugins)){const r=Ct(t.plugins,e);if(null!==r)return Pt(r,e,Tt(t),"",0,[])}const r=ht(e,St(t),Rt(t),At(t));return null!==r?r:gt(e,Tt(t),"",0,[])}const Mt={AsymmetricMatcher:Ye.default,ConvertAnsi:Ze.default,DOMCollection:et.default,DOMElement:tt.default,Immutable:rt.default,ReactElement:nt.default,ReactTestComponent:ot.default};Qe=o.plugins=Mt;var It=_t;Ge=o.default=It;var Bt=t({__proto__:null,get DEFAULT_OPTIONS(){return Ot},format:Je,get plugins(){return Qe},get default(){return Ge}},[o]);function Ft(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const kt=(e,t,r,n,o,a,i)=>{const l=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let c=i(s,r,l,o,a);return"string"!=typeof s&&(-1!==c.indexOf("\n")&&(c=r.spacingOuter+l+c+r.spacingOuter+n),c="{"+c+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+c+u.value.close})).join("")},Nt=(e,t,r,n,o,a)=>e.map((e=>{const i="string"==typeof e?Lt(e,t):a(e,t,r,n,o);return""===i&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+i})).join(""),Lt=(e,t)=>{const r=t.colors.content;return r.open+Ft(e)+r.close},Ut=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+Ft(e)+"--\x3e"+r.close},Dt=(e,t,r,n,o)=>{const a=n.colors.tag;return a.open+"<"+e+(t&&a.close+t+n.spacingOuter+o+a.open)+(r?">"+a.close+r+n.spacingOuter+o+a.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+a.close},Ht=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},zt=3,Wt=8,$t=11,Vt=/^((HTML|SVG)\w*)?Element$/,Gt=e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,o="string"==typeof n&&n.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is");return 1===r&&(Vt.test(t)||o)||r===zt&&"Text"===t||r===Wt&&"Comment"===t||r===$t&&"DocumentFragment"===t};function Jt(e){return e.nodeType===$t}function Qt(e){return{test:e=>{var t;return(null==e||null==(t=e.constructor)?void 0:t.name)&&Gt(e)},serialize:(t,r,n,o,a,i)=>{if(function(e){return e.nodeType===zt}(t))return Lt(t.data,r);if(function(e){return e.nodeType===Wt}(t))return Ut(t.data,r);const l=Jt(t)?"DocumentFragment":t.tagName.toLowerCase();return++o>r.maxDepth?Ht(l,r):Dt(l,kt(Jt(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),Jt(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,o,a,i),Nt(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,o,a,i),r,n)}}}let Xt=null,Kt=null,Yt=null;try{const e=module&&module.require;Kt=e.call(module,"fs").readFileSync,Yt=e.call(module,"@babel/code-frame").codeFrameColumns,Xt=e.call(module,"chalk")}catch{}function Zt(){if(!Kt||!Yt)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),o=n.split(":"),[a,i,l]=[o[0],parseInt(o[1],10),parseInt(o[2],10)];let u="";try{u=Kt(a,"utf-8")}catch{return""}const s=Yt(u,{start:{line:i,column:l}},{highlightCode:!0,linesBelow:0});return Xt.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}const er=3;function tr(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function rr(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function nr(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function or(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const ar=()=>{let e;try{var t,r;e=JSON.parse(null==(t=process)||null==(r=t.env)?void 0:r.COLORS)}catch(e){}return"boolean"==typeof e?e:"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node},{DOMCollection:ir}=Qe,lr=1,ur=8;function sr(e){return e.nodeType!==ur&&(e.nodeType!==lr||!e.matches(fr().defaultIgnore))}function cr(e,t,r){if(void 0===r&&(r={}),e||(e=rr().body),"number"!=typeof t&&(t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:o=sr,...a}=r,i=Je(e,{plugins:[Qt(o),ir],printFunctionName:!1,highlight:ar(),...a});return void 0!==t&&e.outerHTML.length>t?i.slice(0,t)+"...":i}const dr=function(){const e=Zt();e?console.log(cr(...arguments)+"\n\n"+e):console.log(cr(...arguments))};let pr={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=cr(t),n=new Error([e,"Ignored nodes: comments, "+pr.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function fr(){return pr}const mr=["button","meter","output","progress","select","textarea","input"];function br(e){return mr.includes(e.nodeName.toLowerCase())?"":e.nodeType===er?e.textContent:Array.from(e.childNodes).map((e=>br(e))).join("")}function yr(e){let t;return t="label"===e.tagName.toLowerCase()?br(e):e.value||e.textContent,t}function vr(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function hr(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const o=t.getAttribute("aria-labelledby"),a=o?o.split(" "):[];return a.length?a.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:yr(r),formControl:null}:{content:"",formControl:null}})):Array.from(vr(t)).map((e=>({content:yr(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function gr(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function Pr(e,t,r,n){if("string"!=typeof e)return!1;gr(r);const o=n(e);return"string"==typeof r||"number"==typeof r?o.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(o,t):Er(r,o)}function Cr(e,t,r,n){if("string"!=typeof e)return!1;gr(r);const o=n(e);return r instanceof Function?r(o,t):r instanceof RegExp?Er(r,o):o===String(r)}function qr(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function wr(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return qr({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function Er(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function xr(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>e.nodeType===er&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}var Or=Object.prototype.toString;function jr(e){return"function"==typeof e||"[object Function]"===Or.call(e)}var Sr=Math.pow(2,53)-1;function Rr(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),Sr)}function Ar(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!jr(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var o,a=Rr(n.length),i=jr(r)?Object(new r(a)):new Array(a),l=0;l<a;)o=n[l],i[l]=t?t(o,l):o,l+=1;return i.length=a,i}function Tr(e){return Tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tr(e)}function _r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Mr(n.key),n)}}function Mr(e){var t=function(e,t){if("object"!==Tr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Tr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Tr(t)?t:String(t)}var Ir=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t,r){(t=Mr(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&_r(t.prototype,r),n&&_r(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Br="undefined"==typeof Set?Set:Ir;function Fr(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var kr={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},Nr={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function Lr(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=Nr[t])&&void 0!==n&&n.has(r))}))}(e,t)}function Ur(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=kr[Fr(e)];if(void 0!==t)return t;switch(Fr(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||Lr(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||Lr(e,r||""))return r}return t}function Dr(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function Hr(e){return Dr(e)&&"caption"===Fr(e)}function zr(e){return Dr(e)&&"input"===Fr(e)}function Wr(e){return Dr(e)&&"legend"===Fr(e)}function $r(e){return function(e){return Dr(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===Fr(e)}function Vr(e,t){if(Dr(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function Gr(e,t){return!!Dr(e)&&-1!==t.indexOf(Ur(e))}function Jr(e,t){if(!Dr(e))return!1;if("range"===t)return Gr(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function Qr(e,t){var r=Ar(e.querySelectorAll(t));return Vr(e,"aria-owns").forEach((function(e){r.push.apply(r,Ar(e.querySelectorAll(t)))})),r}function Xr(e){return Dr(t=e)&&"select"===Fr(t)?e.selectedOptions||Qr(e,"[selected]"):Qr(e,'[aria-selected="true"]');var t}function Kr(e){return zr(e)||Dr(t=e)&&"textarea"===Fr(t)?e.value:e.textContent||"";var t}function Yr(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function Zr(e){var t=Fr(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function en(e){if(Zr(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&Dr(e)){var r=en(e);null!==r&&(t=r)}})),t}function tn(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):en(e)}function rn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new Br,n=function(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}(e),o=t.compute,a=void 0===o?"name":o,i=t.computedStyleSupportsPseudoElements,l=void 0===i?void 0!==t.getComputedStyle:i,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,c=t.hidden,d=void 0!==c&&c;function p(e,t){var r,n,o="";if(Dr(e)&&l){var a=Yr(s(e,"::before"));o="".concat(a," ").concat(o)}if((function(e){return Dr(e)&&"slot"===Fr(e)}(e)?0===(n=(r=e).assignedNodes()).length?Ar(r.childNodes):n:Ar(e.childNodes).concat(Vr(e,"aria-owns"))).forEach((function(e){var r=b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),n="inline"!==(Dr(e)?s(e).getPropertyValue("display"):"inline")?" ":"";o+="".concat(n).concat(r).concat(n)})),Dr(e)&&l){var i=Yr(s(e,"::after"));o="".concat(o," ").concat(i)}return o.trim()}function f(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}function m(e){if(!Dr(e))return null;if(function(e){return Dr(e)&&"fieldset"===Fr(e)}(e)){r.add(e);for(var t=Ar(e.childNodes),n=0;n<t.length;n+=1){var o=t[n];if(Wr(o))return b(o,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(function(e){return Dr(e)&&"table"===Fr(e)}(e)){r.add(e);for(var a=Ar(e.childNodes),i=0;i<a.length;i+=1){var l=a[i];if(Hr(l))return b(l,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(function(e){return Dr(e)&&"svg"===Fr(e)}(e)){r.add(e);for(var u=Ar(e.childNodes),s=0;s<u.length;s+=1){var c=u[s];if($r(c))return c.textContent}return null}if("img"===Fr(e)||"area"===Fr(e)){var d=f(e,"alt");if(null!==d)return d}else if(function(e){return Dr(e)&&"optgroup"===Fr(e)}(e)){var m=f(e,"label");if(null!==m)return m}}if(zr(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var y=f(e,"value");if(null!==y)return y;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var v,h,g=null===(h=(v=e).labels)?h:void 0!==h?Ar(h):Zr(v)?Ar(v.ownerDocument.querySelectorAll("label")).filter((function(e){return tn(e)===v})):null;if(null!==g&&0!==g.length)return r.add(e),Ar(g).map((function(e){return b(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(zr(e)&&"image"===e.type){var P=f(e,"alt");if(null!==P)return P;var C=f(e,"title");return null!==C?C:"Submit Query"}if(Gr(e,["button"])){var q=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});if(""!==q)return q}return null}function b(e,t){if(r.has(e))return"";if(!d&&function(e,t){if(!Dr(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}(e,s)&&!t.isReferenced)return r.add(e),"";var n=Dr(e)?e.getAttributeNode("aria-labelledby"):null,o=null===n||r.has(n)?[]:Vr(e,"aria-labelledby");if("name"===a&&!t.isReferenced&&o.length>0)return r.add(n),o.map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var i,l=t.recursion&&(Gr(i=e,["button","combobox","listbox","textbox"])||Jr(i,"range"))&&"name"===a;if(!l){var u=(Dr(e)&&e.getAttribute("aria-label")||"").trim();if(""!==u&&"name"===a)return r.add(e),u;if(!function(e){return Gr(e,["none","presentation"])}(e)){var c=m(e);if(null!==c)return r.add(e),c}}if(Gr(e,["menu"]))return r.add(e),"";if(l||t.isEmbeddedInLabel||t.isReferenced){if(Gr(e,["combobox","listbox"])){r.add(e);var y=Xr(e);return 0===y.length?zr(e)?e.value:"":Ar(y).map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(Jr(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(Gr(e,["textbox"]))return r.add(e),Kr(e)}if(function(e){return Gr(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}(e)||Dr(e)&&t.isReferenced||function(e){return Hr(e)}(e)){var v=p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});if(""!==v)return r.add(e),v}if(e.nodeType===e.TEXT_NODE)return r.add(e),e.textContent||"";if(t.recursion)return r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});var h=function(e){return Dr(e)?f(e,"title"):null}(e);return null!==h?(r.add(e),h):(r.add(e),"")}return b(e,{isEmbeddedInLabel:!1,isReferenced:"description"===a,recursion:!1}).trim().replace(/\s\s+/g," ")}function nn(e){return nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nn(e)}function on(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function an(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?on(Object(r),!0).forEach((function(t){ln(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):on(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ln(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==nn(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==nn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===nn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function un(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Vr(e,"aria-describedby").map((function(e){return rn(e,an(an({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function sn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Gr(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])?"":rn(e,t)}var cn={},dn={},pn={},fn={};Object.defineProperty(fn,"__esModule",{value:!0}),fn.default=void 0;var mn=function(){var e=this,t=0,r={"@@iterator":function(){return r},next:function(){if(t<e.length){var r=e[t];return t+=1,{done:!1,value:r}}return{done:!0}}};return r};fn.default=mn,Object.defineProperty(pn,"__esModule",{value:!0}),pn.default=function(e,t){"function"==typeof Symbol&&"symbol"===yn(Symbol.iterator)&&Object.defineProperty(e,Symbol.iterator,{value:bn.default.bind(t)});return e};var bn=function(e){return e&&e.__esModule?e:{default:e}}(fn);function yn(e){return yn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yn(e)}Object.defineProperty(dn,"__esModule",{value:!0}),dn.default=void 0;var vn=function(e){return e&&e.__esModule?e:{default:e}}(pn);function hn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||gn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gn(e,t){if(e){if("string"==typeof e)return Pn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Pn(e,t):void 0}}function Pn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Cn=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],qn={entries:function(){return Cn},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=gn(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Cn);try{for(n.s();!(t=n.n()).done;){var o=hn(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Cn)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Cn.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!qn.get(e)},keys:function(){return Cn.map((function(e){return hn(e,1)[0]}))},values:function(){return Cn.map((function(e){return hn(e,2)[1]}))}},wn=(0,vn.default)(qn,qn.entries());dn.default=wn;var En={};Object.defineProperty(En,"__esModule",{value:!0}),En.default=void 0;var xn=function(e){return e&&e.__esModule?e:{default:e}}(pn);function On(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||jn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jn(e,t){if(e){if("string"==typeof e)return Sn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Sn(e,t):void 0}}function Sn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Rn=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],An={entries:function(){return Rn},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=jn(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Rn);try{for(n.s();!(t=n.n()).done;){var o=On(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Rn)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Rn.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!An.get(e)},keys:function(){return Rn.map((function(e){return On(e,1)[0]}))},values:function(){return Rn.map((function(e){return On(e,2)[1]}))}},Tn=(0,xn.default)(An,An.entries());En.default=Tn;var _n={},Mn={},In={};Object.defineProperty(In,"__esModule",{value:!0}),In.default=void 0;var Bn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};In.default=Bn;var Fn={};Object.defineProperty(Fn,"__esModule",{value:!0}),Fn.default=void 0;var kn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Fn.default=kn;var Nn={};Object.defineProperty(Nn,"__esModule",{value:!0}),Nn.default=void 0;var Ln={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Nn.default=Ln;var Un={};Object.defineProperty(Un,"__esModule",{value:!0}),Un.default=void 0;var Dn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Un.default=Dn;var Hn={};Object.defineProperty(Hn,"__esModule",{value:!0}),Hn.default=void 0;var zn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Hn.default=zn;var Wn={};Object.defineProperty(Wn,"__esModule",{value:!0}),Wn.default=void 0;var $n={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"rel"},module:"HTML"},{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Wn.default=$n;var Vn={};Object.defineProperty(Vn,"__esModule",{value:!0}),Vn.default=void 0;var Gn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Vn.default=Gn;var Jn={};Object.defineProperty(Jn,"__esModule",{value:!0}),Jn.default=void 0;var Qn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Jn.default=Qn;var Xn={};Object.defineProperty(Xn,"__esModule",{value:!0}),Xn.default=void 0;var Kn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};Xn.default=Kn;var Yn={};Object.defineProperty(Yn,"__esModule",{value:!0}),Yn.default=void 0;var Zn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Yn.default=Zn;var eo={};Object.defineProperty(eo,"__esModule",{value:!0}),eo.default=void 0;var to={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};eo.default=to;var ro={};Object.defineProperty(ro,"__esModule",{value:!0}),ro.default=void 0;var no={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};ro.default=no,Object.defineProperty(Mn,"__esModule",{value:!0}),Mn.default=void 0;var oo=vo(In),ao=vo(Fn),io=vo(Nn),lo=vo(Un),uo=vo(Hn),so=vo(Wn),co=vo(Vn),po=vo(Jn),fo=vo(Xn),mo=vo(Yn),bo=vo(eo),yo=vo(ro);function vo(e){return e&&e.__esModule?e:{default:e}}var ho=[["command",oo.default],["composite",ao.default],["input",io.default],["landmark",lo.default],["range",uo.default],["roletype",so.default],["section",co.default],["sectionhead",po.default],["select",fo.default],["structure",mo.default],["widget",bo.default],["window",yo.default]];Mn.default=ho;var go={},Po={};Object.defineProperty(Po,"__esModule",{value:!0}),Po.default=void 0;var Co={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Po.default=Co;var qo={};Object.defineProperty(qo,"__esModule",{value:!0}),qo.default=void 0;var wo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};qo.default=wo;var Eo={};Object.defineProperty(Eo,"__esModule",{value:!0}),Eo.default=void 0;var xo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Eo.default=xo;var Oo={};Object.defineProperty(Oo,"__esModule",{value:!0}),Oo.default=void 0;var jo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};Oo.default=jo;var So={};Object.defineProperty(So,"__esModule",{value:!0}),So.default=void 0;var Ro={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};So.default=Ro;var Ao={};Object.defineProperty(Ao,"__esModule",{value:!0}),Ao.default=void 0;var To={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ao.default=To;var _o={};Object.defineProperty(_o,"__esModule",{value:!0}),_o.default=void 0;var Mo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-pressed"},{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"false"}],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"true"}],constraints:["direct descendant of details element with the open attribute defined"],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};_o.default=Mo;var Io={};Object.defineProperty(Io,"__esModule",{value:!0}),Io.default=void 0;var Bo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Io.default=Bo;var Fo={};Object.defineProperty(Fo,"__esModule",{value:!0}),Fo.default=void 0;var ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["descendant of table"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fo.default=ko;var No={};Object.defineProperty(No,"__esModule",{value:!0}),No.default=void 0;var Lo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};No.default=Lo;var Uo={};Object.defineProperty(Uo,"__esModule",{value:!0}),Uo.default=void 0;var Do={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Uo.default=Do;var Ho={};Object.defineProperty(Ho,"__esModule",{value:!0}),Ho.default=void 0;var zo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{attributes:[{name:"scope",value:"col"}],concept:{name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};Ho.default=zo;var Wo={};Object.defineProperty(Wo,"__esModule",{value:!0}),Wo.default=void 0;var $o={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{name:"size",value:1}],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};Wo.default=$o;var Vo={};Object.defineProperty(Vo,"__esModule",{value:!0}),Vo.default=void 0;var Go={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Vo.default=Go;var Jo={};Object.defineProperty(Jo,"__esModule",{value:!0}),Jo.default=void 0;var Qo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Jo.default=Qo;var Xo={};Object.defineProperty(Xo,"__esModule",{value:!0}),Xo.default=void 0;var Ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Xo.default=Ko;var Yo={};Object.defineProperty(Yo,"__esModule",{value:!0}),Yo.default=void 0;var Zo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Yo.default=Zo;var ea={};Object.defineProperty(ea,"__esModule",{value:!0}),ea.default=void 0;var ta={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};ea.default=ta;var ra={};Object.defineProperty(ra,"__esModule",{value:!0}),ra.default=void 0;var na={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};ra.default=na;var oa={};Object.defineProperty(oa,"__esModule",{value:!0}),oa.default=void 0;var aa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"body"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};oa.default=aa;var ia={};Object.defineProperty(ia,"__esModule",{value:!0}),ia.default=void 0;var la={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ia.default=la;var ua={};Object.defineProperty(ua,"__esModule",{value:!0}),ua.default=void 0;var sa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};ua.default=sa;var ca={};Object.defineProperty(ca,"__esModule",{value:!0}),ca.default=void 0;var da={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ca.default=da;var pa={};Object.defineProperty(pa,"__esModule",{value:!0}),pa.default=void 0;var fa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};pa.default=fa;var ma={};Object.defineProperty(ma,"__esModule",{value:!0}),ma.default=void 0;var ba={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"span"},module:"HTML"},{concept:{name:"div"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ma.default=ba;var ya={};Object.defineProperty(ya,"__esModule",{value:!0}),ya.default=void 0;var va={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"grid"}],name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};ya.default=va;var ha={};Object.defineProperty(ha,"__esModule",{value:!0}),ha.default=void 0;var ga={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"gridcell"}],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};ha.default=ga;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.default=void 0;var Ca={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Pa.default=Ca;var qa={};Object.defineProperty(qa,"__esModule",{value:!0}),qa.default=void 0;var wa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};qa.default=wa;var Ea={};Object.defineProperty(Ea,"__esModule",{value:!0}),Ea.default=void 0;var xa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ea.default=xa;var Oa={};Object.defineProperty(Oa,"__esModule",{value:!0}),Oa.default=void 0;var ja={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Oa.default=ja;var Sa={};Object.defineProperty(Sa,"__esModule",{value:!0}),Sa.default=void 0;var Ra={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"area"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"link"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Sa.default=Ra;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0}),Aa.default=void 0;var Ta={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};Aa.default=Ta;var _a={};Object.defineProperty(_a,"__esModule",{value:!0}),_a.default=void 0;var Ma={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"},{name:"multiple"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:[">1"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};_a.default=Ma;var Ia={};Object.defineProperty(Ia,"__esModule",{value:!0}),Ia.default=void 0;var Ba={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol, ul or menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ia.default=Ba;var Fa={};Object.defineProperty(Fa,"__esModule",{value:!0}),Fa.default=void 0;var ka={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fa.default=ka;var Na={};Object.defineProperty(Na,"__esModule",{value:!0}),Na.default=void 0;var La={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Na.default=La;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0}),Ua.default=void 0;var Da={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ua.default=Da;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0}),Ha.default=void 0;var za={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ha.default=za;var Wa={};Object.defineProperty(Wa,"__esModule",{value:!0}),Wa.default=void 0;var $a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Wa.default=$a;var Va={};Object.defineProperty(Va,"__esModule",{value:!0}),Va.default=void 0;var Ga={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};Va.default=Ga;var Ja={};Object.defineProperty(Ja,"__esModule",{value:!0}),Ja.default=void 0;var Qa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"menuitem"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Ja.default=Qa;var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0}),Xa.default=void 0;var Ka={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};Xa.default=Ka;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0}),Ya.default=void 0;var Za={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};Ya.default=Za;var ei={};Object.defineProperty(ei,"__esModule",{value:!0}),ei.default=void 0;var ti={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};ei.default=ti;var ri={};Object.defineProperty(ri,"__esModule",{value:!0}),ri.default=void 0;var ni={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ri.default=ni;var oi={};Object.defineProperty(oi,"__esModule",{value:!0}),oi.default=void 0;var ai={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};oi.default=ai;var ii={};Object.defineProperty(ii,"__esModule",{value:!0}),ii.default=void 0;var li={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ii.default=li;var ui={};Object.defineProperty(ui,"__esModule",{value:!0}),ui.default=void 0;var si={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};ui.default=si;var ci={};Object.defineProperty(ci,"__esModule",{value:!0}),ci.default=void 0;var di={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ci.default=di;var pi={};Object.defineProperty(pi,"__esModule",{value:!0}),pi.default=void 0;var fi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};pi.default=fi;var mi={};Object.defineProperty(mi,"__esModule",{value:!0}),mi.default=void 0;var bi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};mi.default=bi;var yi={};Object.defineProperty(yi,"__esModule",{value:!0}),yi.default=void 0;var vi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};yi.default=vi;var hi={};Object.defineProperty(hi,"__esModule",{value:!0}),hi.default=void 0;var gi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};hi.default=gi;var Pi={};Object.defineProperty(Pi,"__esModule",{value:!0}),Pi.default=void 0;var Ci={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}},{concept:{name:"frame"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Pi.default=Ci;var qi={};Object.defineProperty(qi,"__esModule",{value:!0}),qi.default=void 0;var wi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};qi.default=wi;var Ei={};Object.defineProperty(Ei,"__esModule",{value:!0}),Ei.default=void 0;var xi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};Ei.default=xi;var Oi={};Object.defineProperty(Oi,"__esModule",{value:!0}),Oi.default=void 0;var ji={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"rowgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row","rowgroup"],requiredContextRole:["row","rowgroup"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};Oi.default=ji;var Si={};Object.defineProperty(Si,"__esModule",{value:!0}),Si.default=void 0;var Ri={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};Si.default=Ri;var Ai={};Object.defineProperty(Ai,"__esModule",{value:!0}),Ai.default=void 0;var Ti={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ai.default=Ti;var _i={};Object.defineProperty(_i,"__esModule",{value:!0}),_i.default=void 0;var Mi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};_i.default=Mi;var Ii={};Object.defineProperty(Ii,"__esModule",{value:!0}),Ii.default=void 0;var Bi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Ii.default=Bi;var Fi={};Object.defineProperty(Fi,"__esModule",{value:!0}),Fi.default=void 0;var ki={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};Fi.default=ki;var Ni={};Object.defineProperty(Ni,"__esModule",{value:!0}),Ni.default=void 0;var Li={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};Ni.default=Li;var Ui={};Object.defineProperty(Ui,"__esModule",{value:!0}),Ui.default=void 0;var Di={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ui.default=Di;var Hi={};Object.defineProperty(Hi,"__esModule",{value:!0}),Hi.default=void 0;var zi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Hi.default=zi;var Wi={};Object.defineProperty(Wi,"__esModule",{value:!0}),Wi.default=void 0;var $i={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Wi.default=$i;var Vi={};Object.defineProperty(Vi,"__esModule",{value:!0}),Vi.default=void 0;var Gi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Vi.default=Gi;var Ji={};Object.defineProperty(Ji,"__esModule",{value:!0}),Ji.default=void 0;var Qi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};Ji.default=Qi;var Xi={};Object.defineProperty(Xi,"__esModule",{value:!0}),Xi.default=void 0;var Ki={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};Xi.default=Ki;var Yi={};Object.defineProperty(Yi,"__esModule",{value:!0}),Yi.default=void 0;var Zi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};Yi.default=Zi;var el={};Object.defineProperty(el,"__esModule",{value:!0}),el.default=void 0;var tl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};el.default=tl;var rl={};Object.defineProperty(rl,"__esModule",{value:!0}),rl.default=void 0;var nl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};rl.default=nl;var ol={};Object.defineProperty(ol,"__esModule",{value:!0}),ol.default=void 0;var al={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ol.default=al;var il={};Object.defineProperty(il,"__esModule",{value:!0}),il.default=void 0;var ll={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};il.default=ll;var ul={};Object.defineProperty(ul,"__esModule",{value:!0}),ul.default=void 0;var sl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ul.default=sl;var cl={};Object.defineProperty(cl,"__esModule",{value:!0}),cl.default=void 0;var dl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};cl.default=dl;var pl={};Object.defineProperty(pl,"__esModule",{value:!0}),pl.default=void 0;var fl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};pl.default=fl;var ml={};Object.defineProperty(ml,"__esModule",{value:!0}),ml.default=void 0;var bl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ml.default=bl;var yl={};Object.defineProperty(yl,"__esModule",{value:!0}),yl.default=void 0;var vl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};yl.default=vl;var hl={};Object.defineProperty(hl,"__esModule",{value:!0}),hl.default=void 0;var gl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};hl.default=gl;var Pl={};Object.defineProperty(Pl,"__esModule",{value:!0}),Pl.default=void 0;var Cl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};Pl.default=Cl,Object.defineProperty(go,"__esModule",{value:!0}),go.default=void 0;var ql=Xu(Po),wl=Xu(qo),El=Xu(Eo),xl=Xu(Oo),Ol=Xu(So),jl=Xu(Ao),Sl=Xu(_o),Rl=Xu(Io),Al=Xu(Fo),Tl=Xu(No),_l=Xu(Uo),Ml=Xu(Ho),Il=Xu(Wo),Bl=Xu(Vo),Fl=Xu(Jo),kl=Xu(Xo),Nl=Xu(Yo),Ll=Xu(ea),Ul=Xu(ra),Dl=Xu(oa),Hl=Xu(ia),zl=Xu(ua),Wl=Xu(ca),$l=Xu(pa),Vl=Xu(ma),Gl=Xu(ya),Jl=Xu(ha),Ql=Xu(Pa),Xl=Xu(qa),Kl=Xu(Ea),Yl=Xu(Oa),Zl=Xu(Sa),eu=Xu(Aa),tu=Xu(_a),ru=Xu(Ia),nu=Xu(Fa),ou=Xu(Na),au=Xu(Ua),iu=Xu(Ha),lu=Xu(Wa),uu=Xu(Va),su=Xu(Ja),cu=Xu(Xa),du=Xu(Ya),pu=Xu(ei),fu=Xu(ri),mu=Xu(oi),bu=Xu(ii),yu=Xu(ui),vu=Xu(ci),hu=Xu(pi),gu=Xu(mi),Pu=Xu(yi),Cu=Xu(hi),qu=Xu(Pi),wu=Xu(qi),Eu=Xu(Ei),xu=Xu(Oi),Ou=Xu(Si),ju=Xu(Ai),Su=Xu(_i),Ru=Xu(Ii),Au=Xu(Fi),Tu=Xu(Ni),_u=Xu(Ui),Mu=Xu(Hi),Iu=Xu(Wi),Bu=Xu(Vi),Fu=Xu(Ji),ku=Xu(Xi),Nu=Xu(Yi),Lu=Xu(el),Uu=Xu(rl),Du=Xu(ol),Hu=Xu(il),zu=Xu(ul),Wu=Xu(cl),$u=Xu(pl),Vu=Xu(ml),Gu=Xu(yl),Ju=Xu(hl),Qu=Xu(Pl);function Xu(e){return e&&e.__esModule?e:{default:e}}var Ku=[["alert",ql.default],["alertdialog",wl.default],["application",El.default],["article",xl.default],["banner",Ol.default],["blockquote",jl.default],["button",Sl.default],["caption",Rl.default],["cell",Al.default],["checkbox",Tl.default],["code",_l.default],["columnheader",Ml.default],["combobox",Il.default],["complementary",Bl.default],["contentinfo",Fl.default],["definition",kl.default],["deletion",Nl.default],["dialog",Ll.default],["directory",Ul.default],["document",Dl.default],["emphasis",Hl.default],["feed",zl.default],["figure",Wl.default],["form",$l.default],["generic",Vl.default],["grid",Gl.default],["gridcell",Jl.default],["group",Ql.default],["heading",Xl.default],["img",Kl.default],["insertion",Yl.default],["link",Zl.default],["list",eu.default],["listbox",tu.default],["listitem",ru.default],["log",nu.default],["main",ou.default],["marquee",au.default],["math",iu.default],["menu",lu.default],["menubar",uu.default],["menuitem",su.default],["menuitemcheckbox",cu.default],["menuitemradio",du.default],["meter",pu.default],["navigation",fu.default],["none",mu.default],["note",bu.default],["option",yu.default],["paragraph",vu.default],["presentation",hu.default],["progressbar",gu.default],["radio",Pu.default],["radiogroup",Cu.default],["region",qu.default],["row",wu.default],["rowgroup",Eu.default],["rowheader",xu.default],["scrollbar",Ou.default],["search",ju.default],["searchbox",Su.default],["separator",Ru.default],["slider",Au.default],["spinbutton",Tu.default],["status",_u.default],["strong",Mu.default],["subscript",Iu.default],["superscript",Bu.default],["switch",Fu.default],["tab",ku.default],["table",Nu.default],["tablist",Lu.default],["tabpanel",Uu.default],["term",Du.default],["textbox",Hu.default],["time",zu.default],["timer",Wu.default],["toolbar",$u.default],["tooltip",Vu.default],["tree",Gu.default],["treegrid",Ju.default],["treeitem",Qu.default]];go.default=Ku;var Yu={},Zu={};Object.defineProperty(Zu,"__esModule",{value:!0}),Zu.default=void 0;var es={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Zu.default=es;var ts={};Object.defineProperty(ts,"__esModule",{value:!0}),ts.default=void 0;var rs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ts.default=rs;var ns={};Object.defineProperty(ns,"__esModule",{value:!0}),ns.default=void 0;var os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ns.default=os;var as={};Object.defineProperty(as,"__esModule",{value:!0}),as.default=void 0;var is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};as.default=is;var ls={};Object.defineProperty(ls,"__esModule",{value:!0}),ls.default=void 0;var us={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","content"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};ls.default=us;var ss={};Object.defineProperty(ss,"__esModule",{value:!0}),ss.default=void 0;var cs={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};ss.default=cs;var ds={};Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var ps={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ds.default=ps;var fs={};Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var ms={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};fs.default=ms;var bs={};Object.defineProperty(bs,"__esModule",{value:!0}),bs.default=void 0;var ys={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};bs.default=ys;var vs={};Object.defineProperty(vs,"__esModule",{value:!0}),vs.default=void 0;var hs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};vs.default=hs;var gs={};Object.defineProperty(gs,"__esModule",{value:!0}),gs.default=void 0;var Ps={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};gs.default=Ps;var Cs={};Object.defineProperty(Cs,"__esModule",{value:!0}),Cs.default=void 0;var qs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};Cs.default=qs;var ws={};Object.defineProperty(ws,"__esModule",{value:!0}),ws.default=void 0;var Es={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ws.default=Es;var xs={};Object.defineProperty(xs,"__esModule",{value:!0}),xs.default=void 0;var Os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};xs.default=Os;var js={};Object.defineProperty(js,"__esModule",{value:!0}),js.default=void 0;var Ss={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};js.default=Ss;var Rs={};Object.defineProperty(Rs,"__esModule",{value:!0}),Rs.default=void 0;var As={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Rs.default=As;var Ts={};Object.defineProperty(Ts,"__esModule",{value:!0}),Ts.default=void 0;var _s={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ts.default=_s;var Ms={};Object.defineProperty(Ms,"__esModule",{value:!0}),Ms.default=void 0;var Is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ms.default=Is;var Bs={};Object.defineProperty(Bs,"__esModule",{value:!0}),Bs.default=void 0;var Fs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Bs.default=Fs;var ks={};Object.defineProperty(ks,"__esModule",{value:!0}),ks.default=void 0;var Ns={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ks.default=Ns;var Ls={};Object.defineProperty(Ls,"__esModule",{value:!0}),Ls.default=void 0;var Us={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ls.default=Us;var Ds={};Object.defineProperty(Ds,"__esModule",{value:!0}),Ds.default=void 0;var Hs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ds.default=Hs;var zs={};Object.defineProperty(zs,"__esModule",{value:!0}),zs.default=void 0;var Ws={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};zs.default=Ws;var $s={};Object.defineProperty($s,"__esModule",{value:!0}),$s.default=void 0;var Vs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};$s.default=Vs;var Gs={};Object.defineProperty(Gs,"__esModule",{value:!0}),Gs.default=void 0;var Js={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Gs.default=Js;var Qs={};Object.defineProperty(Qs,"__esModule",{value:!0}),Qs.default=void 0;var Xs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Qs.default=Xs;var Ks={};Object.defineProperty(Ks,"__esModule",{value:!0}),Ks.default=void 0;var Ys={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ks.default=Ys;var Zs={};Object.defineProperty(Zs,"__esModule",{value:!0}),Zs.default=void 0;var ec={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Zs.default=ec;var tc={};Object.defineProperty(tc,"__esModule",{value:!0}),tc.default=void 0;var rc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};tc.default=rc;var nc={};Object.defineProperty(nc,"__esModule",{value:!0}),nc.default=void 0;var oc={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};nc.default=oc;var ac={};Object.defineProperty(ac,"__esModule",{value:!0}),ac.default=void 0;var ic={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};ac.default=ic;var lc={};Object.defineProperty(lc,"__esModule",{value:!0}),lc.default=void 0;var uc={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};lc.default=uc;var sc={};Object.defineProperty(sc,"__esModule",{value:!0}),sc.default=void 0;var cc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};sc.default=cc;var dc={};Object.defineProperty(dc,"__esModule",{value:!0}),dc.default=void 0;var pc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};dc.default=pc;var fc={};Object.defineProperty(fc,"__esModule",{value:!0}),fc.default=void 0;var mc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};fc.default=mc;var bc={};Object.defineProperty(bc,"__esModule",{value:!0}),bc.default=void 0;var yc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};bc.default=yc;var vc={};Object.defineProperty(vc,"__esModule",{value:!0}),vc.default=void 0;var hc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};vc.default=hc;var gc={};Object.defineProperty(gc,"__esModule",{value:!0}),gc.default=void 0;var Pc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};gc.default=Pc;var Cc={};Object.defineProperty(Cc,"__esModule",{value:!0}),Cc.default=void 0;var qc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Cc.default=qc,Object.defineProperty(Yu,"__esModule",{value:!0}),Yu.default=void 0;var wc=ud(Zu),Ec=ud(ts),xc=ud(ns),Oc=ud(as),jc=ud(ls),Sc=ud(ss),Rc=ud(ds),Ac=ud(fs),Tc=ud(bs),_c=ud(vs),Mc=ud(gs),Ic=ud(Cs),Bc=ud(ws),Fc=ud(xs),kc=ud(js),Nc=ud(Rs),Lc=ud(Ts),Uc=ud(Ms),Dc=ud(Bs),Hc=ud(ks),zc=ud(Ls),Wc=ud(Ds),$c=ud(zs),Vc=ud($s),Gc=ud(Gs),Jc=ud(Qs),Qc=ud(Ks),Xc=ud(Zs),Kc=ud(tc),Yc=ud(nc),Zc=ud(ac),ed=ud(lc),td=ud(sc),rd=ud(dc),nd=ud(fc),od=ud(bc),ad=ud(vc),id=ud(gc),ld=ud(Cc);function ud(e){return e&&e.__esModule?e:{default:e}}var sd=[["doc-abstract",wc.default],["doc-acknowledgments",Ec.default],["doc-afterword",xc.default],["doc-appendix",Oc.default],["doc-backlink",jc.default],["doc-biblioentry",Sc.default],["doc-bibliography",Rc.default],["doc-biblioref",Ac.default],["doc-chapter",Tc.default],["doc-colophon",_c.default],["doc-conclusion",Mc.default],["doc-cover",Ic.default],["doc-credit",Bc.default],["doc-credits",Fc.default],["doc-dedication",kc.default],["doc-endnote",Nc.default],["doc-endnotes",Lc.default],["doc-epigraph",Uc.default],["doc-epilogue",Dc.default],["doc-errata",Hc.default],["doc-example",zc.default],["doc-footnote",Wc.default],["doc-foreword",$c.default],["doc-glossary",Vc.default],["doc-glossref",Gc.default],["doc-index",Jc.default],["doc-introduction",Qc.default],["doc-noteref",Xc.default],["doc-notice",Kc.default],["doc-pagebreak",Yc.default],["doc-pagelist",Zc.default],["doc-part",ed.default],["doc-preface",td.default],["doc-prologue",rd.default],["doc-pullquote",nd.default],["doc-qna",od.default],["doc-subtitle",ad.default],["doc-tip",id.default],["doc-toc",ld.default]];Yu.default=sd;var cd={},dd={};Object.defineProperty(dd,"__esModule",{value:!0}),dd.default=void 0;var pd={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-object"}},{module:"ARIA",concept:{name:"img"}},{module:"ARIA",concept:{name:"article"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};dd.default=pd;var fd={};Object.defineProperty(fd,"__esModule",{value:!0}),fd.default=void 0;var md={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-document"}},{module:"ARIA",concept:{name:"group"}},{module:"ARIA",concept:{name:"img"}},{module:"GRAPHICS",concept:{name:"graphics-symbol"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};fd.default=md;var bd={};Object.defineProperty(bd,"__esModule",{value:!0}),bd.default=void 0;var yd={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};bd.default=yd,Object.defineProperty(cd,"__esModule",{value:!0}),cd.default=void 0;var vd=Pd(dd),hd=Pd(fd),gd=Pd(bd);function Pd(e){return e&&e.__esModule?e:{default:e}}var Cd=[["graphics-document",vd.default],["graphics-object",hd.default],["graphics-symbol",gd.default]];cd.default=Cd,Object.defineProperty(_n,"__esModule",{value:!0}),_n.default=void 0;var qd=jd(Mn),wd=jd(go),Ed=jd(Yu),xd=jd(cd),Od=jd(pn);function jd(e){return e&&e.__esModule?e:{default:e}}function Sd(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rd(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Td(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}function Ad(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||Td(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Td(e,t){if(e){if("string"==typeof e)return _d(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_d(e,t):void 0}}function _d(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Md=[].concat(qd.default,wd.default,Ed.default,xd.default);Md.forEach((function(e){var t,r=Ad(e,2)[1],n=Rd(r.superClass);try{for(n.s();!(t=n.n()).done;){var o,a=Rd(t.value);try{var i=function(){var e=o.value,t=Md.find((function(t){return Ad(t,1)[0]===e}));if(t)for(var n=t[1],a=0,i=Object.keys(n.props);a<i.length;a++){var l=i[a];Object.prototype.hasOwnProperty.call(r.props,l)||Object.assign(r.props,Sd({},l,n.props[l]))}};for(a.s();!(o=a.n()).done;)i()}catch(e){a.e(e)}finally{a.f()}}}catch(e){n.e(e)}finally{n.f()}}));var Id={entries:function(){return Md},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=Rd(Md);try{for(n.s();!(t=n.n()).done;){var o=Ad(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Md)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Md.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Id.get(e)},keys:function(){return Md.map((function(e){return Ad(e,1)[0]}))},values:function(){return Md.map((function(e){return Ad(e,2)[1]}))}},Bd=(0,Od.default)(Id,Id.entries());_n.default=Bd;var Fd,kd={},Nd=Object.prototype.toString,Ld=function(e){var t=Nd.call(e),r="[object Arguments]"===t;return r||(r="[object Array]"!==t&&null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Function]"===Nd.call(e.callee)),r};if(!Object.keys){var Ud=Object.prototype.hasOwnProperty,Dd=Object.prototype.toString,Hd=Ld,zd=Object.prototype.propertyIsEnumerable,Wd=!zd.call({toString:null},"toString"),$d=zd.call((function(){}),"prototype"),Vd=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],Gd=function(e){var t=e.constructor;return t&&t.prototype===e},Jd={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},Qd=function(){if("undefined"==typeof window)return!1;for(var e in window)try{if(!Jd["$"+e]&&Ud.call(window,e)&&null!==window[e]&&"object"==typeof window[e])try{Gd(window[e])}catch(e){return!0}}catch(e){return!0}return!1}();Fd=function(e){var t=null!==e&&"object"==typeof e,r="[object Function]"===Dd.call(e),n=Hd(e),o=t&&"[object String]"===Dd.call(e),a=[];if(!t&&!r&&!n)throw new TypeError("Object.keys called on a non-object");if(o&&e.length>0&&!Ud.call(e,0))for(var i=0;i<e.length;++i)a.push(String(i));if(n&&e.length>0)for(var l=0;l<e.length;++l)a.push(String(l));else for(var u in e)$d&&r&&"prototype"===u||!Ud.call(e,u)||a.push(String(u));if(Wd)for(var s=function(e){if("undefined"==typeof window||!Qd)return Gd(e);try{return Gd(e)}catch(e){return!1}}(e),c=0;c<Vd.length;++c)s&&"constructor"===Vd[c]||!Ud.call(e,Vd[c])||a.push(Vd[c]);return a}}var Xd=Fd,Kd=Array.prototype.slice,Yd=Ld,Zd=Object.keys,ep=Zd?function(e){return Zd(e)}:Xd,tp=Object.keys;ep.shim=function(){if(Object.keys){var e=function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2);e||(Object.keys=function(e){return Yd(e)?tp(Kd.call(e)):tp(e)})}else Object.keys=ep;return Object.keys||ep};var rp,np=ep,op=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0},ap="undefined"!=typeof Symbol&&Symbol,ip=op,lp=function(){return"function"==typeof ap&&("function"==typeof Symbol&&("symbol"==typeof ap("foo")&&("symbol"==typeof Symbol("bar")&&ip())))},up={foo:{}},sp=Object,cp=Array.prototype.slice,dp=Object.prototype.toString,pp=function(e){var t=this;if("function"!=typeof t||"[object Function]"!==dp.call(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var r,n=cp.call(arguments,1),o=Math.max(0,t.length-n.length),a=[],i=0;i<o;i++)a.push("$"+i);if(r=Function("binder","return function ("+a.join(",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var o=t.apply(this,n.concat(cp.call(arguments)));return Object(o)===o?o:this}return t.apply(e,n.concat(cp.call(arguments)))})),t.prototype){var l=function(){};l.prototype=t.prototype,r.prototype=new l,l.prototype=null}return r},fp=Function.prototype.bind||pp,mp=fp.call(Function.call,Object.prototype.hasOwnProperty),bp=SyntaxError,yp=Function,vp=TypeError,hp=function(e){try{return yp('"use strict"; return ('+e+").constructor;")()}catch(e){}},gp=Object.getOwnPropertyDescriptor;if(gp)try{gp({},"")}catch(ae){gp=null}var Pp=function(){throw new vp},Cp=gp?function(){try{return Pp}catch(e){try{return gp(arguments,"callee").get}catch(e){return Pp}}}():Pp,qp=lp(),wp={__proto__:up}.foo===up.foo&&!({__proto__:null}instanceof sp),Ep=Object.getPrototypeOf||(wp?function(e){return e.__proto__}:null),xp={},Op="undefined"!=typeof Uint8Array&&Ep?Ep(Uint8Array):rp,jp={"%AggregateError%":"undefined"==typeof AggregateError?rp:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?rp:ArrayBuffer,"%ArrayIteratorPrototype%":qp&&Ep?Ep([][Symbol.iterator]()):rp,"%AsyncFromSyncIteratorPrototype%":rp,"%AsyncFunction%":xp,"%AsyncGenerator%":xp,"%AsyncGeneratorFunction%":xp,"%AsyncIteratorPrototype%":xp,"%Atomics%":"undefined"==typeof Atomics?rp:Atomics,"%BigInt%":"undefined"==typeof BigInt?rp:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?rp:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?rp:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?rp:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?rp:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?rp:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?rp:FinalizationRegistry,"%Function%":yp,"%GeneratorFunction%":xp,"%Int8Array%":"undefined"==typeof Int8Array?rp:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?rp:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?rp:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":qp&&Ep?Ep(Ep([][Symbol.iterator]())):rp,"%JSON%":"object"==typeof JSON?JSON:rp,"%Map%":"undefined"==typeof Map?rp:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&qp&&Ep?Ep((new Map)[Symbol.iterator]()):rp,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?rp:Promise,"%Proxy%":"undefined"==typeof Proxy?rp:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?rp:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?rp:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&qp&&Ep?Ep((new Set)[Symbol.iterator]()):rp,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?rp:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":qp&&Ep?Ep(""[Symbol.iterator]()):rp,"%Symbol%":qp?Symbol:rp,"%SyntaxError%":bp,"%ThrowTypeError%":Cp,"%TypedArray%":Op,"%TypeError%":vp,"%Uint8Array%":"undefined"==typeof Uint8Array?rp:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?rp:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?rp:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?rp:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?rp:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?rp:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?rp:WeakSet};if(Ep)try{null.error}catch(ae){var Sp=Ep(Ep(ae));jp["%Error.prototype%"]=Sp}var Rp=function e(t){var r;if("%AsyncFunction%"===t)r=hp("async function () {}");else if("%GeneratorFunction%"===t)r=hp("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=hp("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&Ep&&(r=Ep(o.prototype))}return jp[t]=r,r},Ap={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Tp=fp,_p=mp,Mp=Tp.call(Function.call,Array.prototype.concat),Ip=Tp.call(Function.apply,Array.prototype.splice),Bp=Tp.call(Function.call,String.prototype.replace),Fp=Tp.call(Function.call,String.prototype.slice),kp=Tp.call(Function.call,RegExp.prototype.exec),Np=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Lp=/\\(\\)?/g,Up=function(e,t){var r,n=e;if(_p(Ap,n)&&(n="%"+(r=Ap[n])[0]+"%"),_p(jp,n)){var o=jp[n];if(o===xp&&(o=Rp(n)),void 0===o&&!t)throw new vp("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:o}}throw new bp("intrinsic "+e+" does not exist!")},Dp=function(e,t){if("string"!=typeof e||0===e.length)throw new vp("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new vp('"allowMissing" argument must be a boolean');if(null===kp(/^%?[^%]*%?$/,e))throw new bp("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=Fp(e,0,1),r=Fp(e,-1);if("%"===t&&"%"!==r)throw new bp("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new bp("invalid intrinsic syntax, expected opening `%`");var n=[];return Bp(e,Np,(function(e,t,r,o){n[n.length]=r?Bp(o,Lp,"$1"):t||e})),n}(e),n=r.length>0?r[0]:"",o=Up("%"+n+"%",t),a=o.name,i=o.value,l=!1,u=o.alias;u&&(n=u[0],Ip(r,Mp([0,1],u)));for(var s=1,c=!0;s<r.length;s+=1){var d=r[s],p=Fp(d,0,1),f=Fp(d,-1);if(('"'===p||"'"===p||"`"===p||'"'===f||"'"===f||"`"===f)&&p!==f)throw new bp("property names with quotes must have matching quotes");if("constructor"!==d&&c||(l=!0),_p(jp,a="%"+(n+="."+d)+"%"))i=jp[a];else if(null!=i){if(!(d in i)){if(!t)throw new vp("base intrinsic for "+e+" exists, but the property is not available.");return}if(gp&&s+1>=r.length){var m=gp(i,d);i=(c=!!m)&&"get"in m&&!("originalValue"in m.get)?m.get:i[d]}else c=_p(i,d),i=i[d];c&&!l&&(jp[a]=i)}}return i},Hp=Dp("%Object.defineProperty%",!0),zp=function(){if(Hp)try{return Hp({},"a",{value:1}),!0}catch(e){return!1}return!1};zp.hasArrayLengthDefineBug=function(){if(!zp())return null;try{return 1!==Hp([],"length",{value:1}).length}catch(e){return!0}};var Wp=zp,$p=np,Vp="function"==typeof Symbol&&"symbol"==typeof Symbol("foo"),Gp=Object.prototype.toString,Jp=Array.prototype.concat,Qp=Object.defineProperty,Xp=Wp(),Kp=Qp&&Xp,Yp=function(e,t,r,n){if(t in e)if(!0===n){if(e[t]===r)return}else if("function"!=typeof(o=n)||"[object Function]"!==Gp.call(o)||!n())return;var o;Kp?Qp(e,t,{configurable:!0,enumerable:!1,value:r,writable:!0}):e[t]=r},Zp=function(e,t){var r=arguments.length>2?arguments[2]:{},n=$p(t);Vp&&(n=Jp.call(n,Object.getOwnPropertySymbols(t)));for(var o=0;o<n.length;o+=1)Yp(e,n[o],t[n[o]],r[n[o]])};Zp.supportsDescriptors=!!Kp;var ef=Zp,tf={exports:{}};!function(e){var t=fp,r=Dp,n=r("%Function.prototype.apply%"),o=r("%Function.prototype.call%"),a=r("%Reflect.apply%",!0)||t.call(o,n),i=r("%Object.getOwnPropertyDescriptor%",!0),l=r("%Object.defineProperty%",!0),u=r("%Math.max%");if(l)try{l({},"a",{value:1})}catch(e){l=null}e.exports=function(e){var r=a(t,o,arguments);i&&l&&(i(r,"length").configurable&&l(r,"length",{value:1+u(0,e.length-(arguments.length-1))}));return r};var s=function(){return a(t,n,arguments)};l?l(e.exports,"apply",{value:s}):e.exports.apply=s}(tf);var rf=Dp,nf=tf.exports,of=nf(rf("String.prototype.indexOf")),af=function(e,t){var r=rf(e,!!t);return"function"==typeof r&&of(e,".prototype.")>-1?nf(r):r},lf=np,uf=op(),sf=af,cf=Object,df=sf("Array.prototype.push"),pf=sf("Object.prototype.propertyIsEnumerable"),ff=uf?Object.getOwnPropertySymbols:null,mf=function(e){if(null==e)throw new TypeError("target must be an object");var t=cf(e);if(1===arguments.length)return t;for(var r=1;r<arguments.length;++r){var n=cf(arguments[r]),o=lf(n),a=uf&&(Object.getOwnPropertySymbols||ff);if(a)for(var i=a(n),l=0;l<i.length;++l){var u=i[l];pf(n,u)&&df(o,u)}for(var s=0;s<o.length;++s){var c=o[s];if(pf(n,c)){var d=n[c];t[c]=d}}}return t},bf=mf,yf=function(){return Object.assign?function(){if(!Object.assign)return!1;for(var e="abcdefghijklmnopqrst",t=e.split(""),r={},n=0;n<t.length;++n)r[t[n]]=t[n];var o=Object.assign({},r),a="";for(var i in o)a+=i;return e!==a}()||function(){if(!Object.assign||!Object.preventExtensions)return!1;var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch(t){return"y"===e[1]}return!1}()?bf:Object.assign:bf},vf=ef,hf=yf,gf=ef,Pf=mf,Cf=yf,qf=function(){var e=hf();return vf(Object,{assign:e},{assign:function(){return Object.assign!==e}}),e},wf=tf.exports.apply(Cf()),Ef=function(){return wf(Object,arguments)};gf(Ef,{getPolyfill:Cf,implementation:Pf,shim:qf});var xf=Ef,Of={exports:{}},jf=function(){return"string"==typeof function(){}.name},Sf=Object.getOwnPropertyDescriptor;if(Sf)try{Sf([],"length")}catch(ae){Sf=null}jf.functionsHaveConfigurableNames=function(){if(!jf()||!Sf)return!1;var e=Sf((function(){}),"name");return!!e&&!!e.configurable};var Rf=Function.prototype.bind;jf.boundFunctionsHaveNames=function(){return jf()&&"function"==typeof Rf&&""!==function(){}.bind().name};var Af=jf;!function(e){var t=Af.functionsHaveConfigurableNames(),r=Object,n=TypeError;e.exports=function(){if(null!=this&&this!==r(this))throw new n("RegExp.prototype.flags getter called on non-object");var e="";return this.hasIndices&&(e+="d"),this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),this.dotAll&&(e+="s"),this.unicode&&(e+="u"),this.unicodeSets&&(e+="v"),this.sticky&&(e+="y"),e},t&&Object.defineProperty&&Object.defineProperty(e.exports,"name",{value:"get flags"})}(Of);var Tf=Of.exports,_f=ef.supportsDescriptors,Mf=Object.getOwnPropertyDescriptor,If=function(){if(_f&&"gim"===/a/gim.flags){var e=Mf(RegExp.prototype,"flags");if(e&&"function"==typeof e.get&&"boolean"==typeof RegExp.prototype.dotAll&&"boolean"==typeof RegExp.prototype.hasIndices){var t="",r={};if(Object.defineProperty(r,"hasIndices",{get:function(){t+="d"}}),Object.defineProperty(r,"sticky",{get:function(){t+="y"}}),"dy"===t)return e.get}}return Tf},Bf=ef.supportsDescriptors,Ff=If,kf=Object.getOwnPropertyDescriptor,Nf=Object.defineProperty,Lf=TypeError,Uf=Object.getPrototypeOf,Df=/a/,Hf=ef,zf=Of.exports,Wf=If,$f=function(){if(!Bf||!Uf)throw new Lf("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");var e=Ff(),t=Uf(Df),r=kf(t,"flags");return r&&r.get===e||Nf(t,"flags",{configurable:!0,enumerable:!1,get:e}),e},Vf=(0,tf.exports)(Wf());Hf(Vf,{getPolyfill:Wf,implementation:zf,shim:$f});var Gf=Vf,Jf={exports:{}},Qf=op,Xf=function(){return Qf()&&!!Symbol.toStringTag},Kf=Xf(),Yf=af("Object.prototype.toString"),Zf=function(e){return!(Kf&&e&&"object"==typeof e&&Symbol.toStringTag in e)&&"[object Arguments]"===Yf(e)},em=function(e){return!!Zf(e)||null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Array]"!==Yf(e)&&"[object Function]"===Yf(e.callee)},tm=function(){return Zf(arguments)}();Zf.isLegacyArguments=em;var rm=tm?Zf:em,nm=n(Object.freeze({__proto__:null,default:{}})),om="function"==typeof Map&&Map.prototype,am=Object.getOwnPropertyDescriptor&&om?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,im=om&&am&&"function"==typeof am.get?am.get:null,lm=om&&Map.prototype.forEach,um="function"==typeof Set&&Set.prototype,sm=Object.getOwnPropertyDescriptor&&um?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,cm=um&&sm&&"function"==typeof sm.get?sm.get:null,dm=um&&Set.prototype.forEach,pm="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,fm="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,mm="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,bm=Boolean.prototype.valueOf,ym=Object.prototype.toString,vm=Function.prototype.toString,hm=String.prototype.match,gm=String.prototype.slice,Pm=String.prototype.replace,Cm=String.prototype.toUpperCase,qm=String.prototype.toLowerCase,wm=RegExp.prototype.test,Em=Array.prototype.concat,xm=Array.prototype.join,Om=Array.prototype.slice,jm=Math.floor,Sm="function"==typeof BigInt?BigInt.prototype.valueOf:null,Rm=Object.getOwnPropertySymbols,Am="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,Tm="function"==typeof Symbol&&"object"==typeof Symbol.iterator,_m="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===Tm||"symbol")?Symbol.toStringTag:null,Mm=Object.prototype.propertyIsEnumerable,Im=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function Bm(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||wm.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-jm(-e):jm(e);if(n!==e){var o=String(n),a=gm.call(t,o.length+1);return Pm.call(o,r,"$&_")+"."+Pm.call(Pm.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return Pm.call(t,r,"$&_")}var Fm=nm,km=Fm.custom,Nm=zm(km)?km:null;function Lm(e,t,r){var n="double"===(r.quoteStyle||t)?'"':"'";return n+e+n}function Um(e){return Pm.call(String(e),/"/g,"&quot;")}function Dm(e){return!("[object Array]"!==Vm(e)||_m&&"object"==typeof e&&_m in e)}function Hm(e){return!("[object RegExp]"!==Vm(e)||_m&&"object"==typeof e&&_m in e)}function zm(e){if(Tm)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!Am)return!1;try{return Am.call(e),!0}catch(e){}return!1}var Wm=Object.prototype.hasOwnProperty||function(e){return e in this};function $m(e,t){return Wm.call(e,t)}function Vm(e){return ym.call(e)}function Gm(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function Jm(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return Jm(gm.call(e,0,t.maxStringLength),t)+n}return Lm(Pm.call(Pm.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Qm),"single",t)}function Qm(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+Cm.call(t.toString(16))}function Xm(e){return"Object("+e+")"}function Km(e){return e+" { ? }"}function Ym(e,t,r,n){return e+" ("+t+") {"+(n?Zm(r,n):xm.call(r,", "))+"}"}function Zm(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+xm.call(e,","+r)+"\n"+t.prev}function eb(e,t){var r=Dm(e),n=[];if(r){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=$m(e,o)?t(e[o],e):""}var a,i="function"==typeof Rm?Rm(e):[];if(Tm){a={};for(var l=0;l<i.length;l++)a["$"+i[l]]=i[l]}for(var u in e)$m(e,u)&&(r&&String(Number(u))===u&&u<e.length||Tm&&a["$"+u]instanceof Symbol||(wm.call(/[^\w$]/,u)?n.push(t(u,e)+": "+t(e[u],e)):n.push(u+": "+t(e[u],e))));if("function"==typeof Rm)for(var s=0;s<i.length;s++)Mm.call(e,i[s])&&n.push("["+t(i[s])+"]: "+t(e[i[s]],e));return n}var tb=Dp,rb=af,nb=function e(t,r,n,o){var a=r||{};if($m(a,"quoteStyle")&&"single"!==a.quoteStyle&&"double"!==a.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if($m(a,"maxStringLength")&&("number"==typeof a.maxStringLength?a.maxStringLength<0&&a.maxStringLength!==1/0:null!==a.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=!$m(a,"customInspect")||a.customInspect;if("boolean"!=typeof i&&"symbol"!==i)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if($m(a,"indent")&&null!==a.indent&&"\t"!==a.indent&&!(parseInt(a.indent,10)===a.indent&&a.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if($m(a,"numericSeparator")&&"boolean"!=typeof a.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var l=a.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return Jm(t,a);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var u=String(t);return l?Bm(t,u):u}if("bigint"==typeof t){var s=String(t)+"n";return l?Bm(t,s):s}var c=void 0===a.depth?5:a.depth;if(void 0===n&&(n=0),n>=c&&c>0&&"object"==typeof t)return Dm(t)?"[Array]":"[Object]";var d=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=xm.call(Array(e.indent+1)," ")}return{base:r,prev:xm.call(Array(t+1),r)}}(a,n);if(void 0===o)o=[];else if(Gm(o,t)>=0)return"[Circular]";function p(t,r,i){if(r&&(o=Om.call(o)).push(r),i){var l={depth:a.depth};return $m(a,"quoteStyle")&&(l.quoteStyle=a.quoteStyle),e(t,l,n+1,o)}return e(t,a,n+1,o)}if("function"==typeof t&&!Hm(t)){var f=function(e){if(e.name)return e.name;var t=hm.call(vm.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),m=eb(t,p);return"[Function"+(f?": "+f:" (anonymous)")+"]"+(m.length>0?" { "+xm.call(m,", ")+" }":"")}if(zm(t)){var b=Tm?Pm.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):Am.call(t);return"object"!=typeof t||Tm?b:Xm(b)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var y="<"+qm.call(String(t.nodeName)),v=t.attributes||[],h=0;h<v.length;h++)y+=" "+v[h].name+"="+Lm(Um(v[h].value),"double",a);return y+=">",t.childNodes&&t.childNodes.length&&(y+="..."),y+="</"+qm.call(String(t.nodeName))+">"}if(Dm(t)){if(0===t.length)return"[]";var g=eb(t,p);return d&&!function(e){for(var t=0;t<e.length;t++)if(Gm(e[t],"\n")>=0)return!1;return!0}(g)?"["+Zm(g,d)+"]":"[ "+xm.call(g,", ")+" ]"}if(function(e){return!("[object Error]"!==Vm(e)||_m&&"object"==typeof e&&_m in e)}(t)){var P=eb(t,p);return"cause"in Error.prototype||!("cause"in t)||Mm.call(t,"cause")?0===P.length?"["+String(t)+"]":"{ ["+String(t)+"] "+xm.call(P,", ")+" }":"{ ["+String(t)+"] "+xm.call(Em.call("[cause]: "+p(t.cause),P),", ")+" }"}if("object"==typeof t&&i){if(Nm&&"function"==typeof t[Nm]&&Fm)return Fm(t,{depth:c-n});if("symbol"!==i&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!im||!e||"object"!=typeof e)return!1;try{im.call(e);try{cm.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var C=[];return lm&&lm.call(t,(function(e,r){C.push(p(r,t,!0)+" => "+p(e,t))})),Ym("Map",im.call(t),C,d)}if(function(e){if(!cm||!e||"object"!=typeof e)return!1;try{cm.call(e);try{im.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var q=[];return dm&&dm.call(t,(function(e){q.push(p(e,t))})),Ym("Set",cm.call(t),q,d)}if(function(e){if(!pm||!e||"object"!=typeof e)return!1;try{pm.call(e,pm);try{fm.call(e,fm)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Km("WeakMap");if(function(e){if(!fm||!e||"object"!=typeof e)return!1;try{fm.call(e,fm);try{pm.call(e,pm)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Km("WeakSet");if(function(e){if(!mm||!e||"object"!=typeof e)return!1;try{return mm.call(e),!0}catch(e){}return!1}(t))return Km("WeakRef");if(function(e){return!("[object Number]"!==Vm(e)||_m&&"object"==typeof e&&_m in e)}(t))return Xm(p(Number(t)));if(function(e){if(!e||"object"!=typeof e||!Sm)return!1;try{return Sm.call(e),!0}catch(e){}return!1}(t))return Xm(p(Sm.call(t)));if(function(e){return!("[object Boolean]"!==Vm(e)||_m&&"object"==typeof e&&_m in e)}(t))return Xm(bm.call(t));if(function(e){return!("[object String]"!==Vm(e)||_m&&"object"==typeof e&&_m in e)}(t))return Xm(p(String(t)));if(!function(e){return!("[object Date]"!==Vm(e)||_m&&"object"==typeof e&&_m in e)}(t)&&!Hm(t)){var w=eb(t,p),E=Im?Im(t)===Object.prototype:t instanceof Object||t.constructor===Object,x=t instanceof Object?"":"null prototype",O=!E&&_m&&Object(t)===t&&_m in t?gm.call(Vm(t),8,-1):x?"Object":"",j=(E||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(O||x?"["+xm.call(Em.call([],O||[],x||[]),": ")+"] ":"");return 0===w.length?j+"{}":d?j+"{"+Zm(w,d)+"}":j+"{ "+xm.call(w,", ")+" }"}return String(t)},ob=tb("%TypeError%"),ab=tb("%WeakMap%",!0),ib=tb("%Map%",!0),lb=rb("WeakMap.prototype.get",!0),ub=rb("WeakMap.prototype.set",!0),sb=rb("WeakMap.prototype.has",!0),cb=rb("Map.prototype.get",!0),db=rb("Map.prototype.set",!0),pb=rb("Map.prototype.has",!0),fb=function(e,t){for(var r,n=e;null!==(r=n.next);n=r)if(r.key===t)return n.next=r.next,r.next=e.next,e.next=r,r},mb=function(){var e,t,r,n={assert:function(e){if(!n.has(e))throw new ob("Side channel does not contain "+nb(e))},get:function(n){if(ab&&n&&("object"==typeof n||"function"==typeof n)){if(e)return lb(e,n)}else if(ib){if(t)return cb(t,n)}else if(r)return function(e,t){var r=fb(e,t);return r&&r.value}(r,n)},has:function(n){if(ab&&n&&("object"==typeof n||"function"==typeof n)){if(e)return sb(e,n)}else if(ib){if(t)return pb(t,n)}else if(r)return function(e,t){return!!fb(e,t)}(r,n);return!1},set:function(n,o){ab&&n&&("object"==typeof n||"function"==typeof n)?(e||(e=new ab),ub(e,n,o)):ib?(t||(t=new ib),db(t,n,o)):(r||(r={key:{},next:null}),function(e,t,r){var n=fb(e,t);n?n.value=r:e.next={key:t,next:e.next,value:r}}(r,n,o))}};return n},bb=Dp,yb=mp,vb=mb(),hb=bb("%TypeError%"),gb={assert:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new hb("`O` is not an object");if("string"!=typeof t)throw new hb("`slot` must be a string");if(vb.assert(e),!gb.has(e,t))throw new hb("`"+t+"` is not present on `O`")},get:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new hb("`O` is not an object");if("string"!=typeof t)throw new hb("`slot` must be a string");var r=vb.get(e);return r&&r["$"+t]},has:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new hb("`O` is not an object");if("string"!=typeof t)throw new hb("`slot` must be a string");var r=vb.get(e);return!!r&&yb(r,"$"+t)},set:function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new hb("`O` is not an object");if("string"!=typeof t)throw new hb("`slot` must be a string");var n=vb.get(e);n||(n={},vb.set(e,n)),n["$"+t]=r}};Object.freeze&&Object.freeze(gb);var Pb,Cb=gb,qb=SyntaxError,wb="object"==typeof StopIteration?StopIteration:null,Eb={}.toString,xb=Array.isArray||function(e){return"[object Array]"==Eb.call(e)},Ob=String.prototype.valueOf,jb=Object.prototype.toString,Sb=Xf(),Rb=function(e){return"string"==typeof e||"object"==typeof e&&(Sb?function(e){try{return Ob.call(e),!0}catch(e){return!1}}(e):"[object String]"===jb.call(e))},Ab="function"==typeof Map&&Map.prototype?Map:null,Tb="function"==typeof Set&&Set.prototype?Set:null;Ab||(Pb=function(){return!1});var _b=Ab?Map.prototype.has:null,Mb=Tb?Set.prototype.has:null;Pb||_b||(Pb=function(){return!1});var Ib,Bb=Pb||function(e){if(!e||"object"!=typeof e)return!1;try{if(_b.call(e),Mb)try{Mb.call(e)}catch(e){return!0}return e instanceof Ab}catch(e){}return!1},Fb="function"==typeof Map&&Map.prototype?Map:null,kb="function"==typeof Set&&Set.prototype?Set:null;kb||(Ib=function(){return!1});var Nb=Fb?Map.prototype.has:null,Lb=kb?Set.prototype.has:null;Ib||Lb||(Ib=function(){return!1});var Ub=Ib||function(e){if(!e||"object"!=typeof e)return!1;try{if(Lb.call(e),Nb)try{Nb.call(e)}catch(e){return!0}return e instanceof kb}catch(e){}return!1},Db=rm,Hb=function(e){if(!wb)throw new qb("this environment lacks StopIteration");Cb.set(e,"[[Done]]",!1);var t={next:function(){var e=Cb.get(this,"[[Iterator]]"),t=Cb.get(e,"[[Done]]");try{return{done:t,value:t?void 0:e.next()}}catch(t){if(Cb.set(e,"[[Done]]",!0),t!==wb)throw t;return{done:!0,value:void 0}}}};return Cb.set(t,"[[Iterator]]",e),t};if(lp()||op()){var zb=Symbol.iterator;Jf.exports=function(e){return null!=e&&void 0!==e[zb]?e[zb]():Db(e)?Array.prototype[zb].call(e):void 0}}else{var Wb=xb,$b=Rb,Vb=Dp,Gb=Vb("%Map%",!0),Jb=Vb("%Set%",!0),Qb=af,Xb=Qb("Array.prototype.push"),Kb=Qb("String.prototype.charCodeAt"),Yb=Qb("String.prototype.slice"),Zb=function(e){var t=0;return{next:function(){var r,n=t>=e.length;return n||(r=e[t],t+=1),{done:n,value:r}}}},ey=function(e,t){if(Wb(e)||Db(e))return Zb(e);if($b(e)){var r=0;return{next:function(){var t=function(e,t){if(t+1>=e.length)return t+1;var r=Kb(e,t);if(r<55296||r>56319)return t+1;var n=Kb(e,t+1);return n<56320||n>57343?t+1:t+2}(e,r),n=Yb(e,r,t);return r=t,{done:t>e.length,value:n}}}}return t&&void 0!==e["_es6-shim iterator_"]?e["_es6-shim iterator_"]():void 0};if(Gb||Jb){var ty=Bb,ry=Ub,ny=Qb("Map.prototype.forEach",!0),oy=Qb("Set.prototype.forEach",!0);if("undefined"==typeof process||!process.versions||!process.versions.node)var ay=Qb("Map.prototype.iterator",!0),iy=Qb("Set.prototype.iterator",!0);var ly=Qb("Map.prototype.@@iterator",!0)||Qb("Map.prototype._es6-shim iterator_",!0),uy=Qb("Set.prototype.@@iterator",!0)||Qb("Set.prototype._es6-shim iterator_",!0);Jf.exports=function(e){return function(e){if(ty(e)){if(ay)return Hb(ay(e));if(ly)return ly(e);if(ny){var t=[];return ny(e,(function(e,r){Xb(t,[r,e])})),Zb(t)}}if(ry(e)){if(iy)return Hb(iy(e));if(uy)return uy(e);if(oy){var r=[];return oy(e,(function(e){Xb(r,e)})),Zb(r)}}}(e)||ey(e)}}else Jf.exports=function(e){if(null!=e)return ey(e,!0)}}var sy=function(e){return e!=e},cy=function(e,t){return 0===e&&0===t?1/e==1/t:e===t||!(!sy(e)||!sy(t))},dy=cy,py=function(){return"function"==typeof Object.is?Object.is:dy},fy=py,my=ef,by=ef,yy=cy,vy=py,hy=function(){var e=fy();return my(Object,{is:e},{is:function(){return Object.is!==e}}),e},gy=(0,tf.exports)(vy(),Object);by(gy,{getPolyfill:vy,implementation:yy,shim:hy});var Py,Cy,qy=gy,wy=Function.prototype.toString,Ey="object"==typeof Reflect&&null!==Reflect&&Reflect.apply;if("function"==typeof Ey&&"function"==typeof Object.defineProperty)try{Py=Object.defineProperty({},"length",{get:function(){throw Cy}}),Cy={},Ey((function(){throw 42}),null,Py)}catch(e){e!==Cy&&(Ey=null)}else Ey=null;var xy=/^\s*class\b/,Oy=function(e){try{var t=wy.call(e);return xy.test(t)}catch(e){return!1}},jy=function(e){try{return!Oy(e)&&(wy.call(e),!0)}catch(e){return!1}},Sy=Object.prototype.toString,Ry="function"==typeof Symbol&&!!Symbol.toStringTag,Ay=!(0 in[,]),Ty=function(){return!1};if("object"==typeof document){var _y=document.all;Sy.call(_y)===Sy.call(document.all)&&(Ty=function(e){if((Ay||!e)&&(void 0===e||"object"==typeof e))try{var t=Sy.call(e);return("[object HTMLAllCollection]"===t||"[object HTML document.all class]"===t||"[object HTMLCollection]"===t||"[object Object]"===t)&&null==e("")}catch(e){}return!1})}var My=Ey?function(e){if(Ty(e))return!0;if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;try{Ey(e,null,Py)}catch(e){if(e!==Cy)return!1}return!Oy(e)&&jy(e)}:function(e){if(Ty(e))return!0;if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;if(Ry)return jy(e);if(Oy(e))return!1;var t=Sy.call(e);return!("[object Function]"!==t&&"[object GeneratorFunction]"!==t&&!/^\[object HTML/.test(t))&&jy(e)},Iy=My,By=Object.prototype.toString,Fy=Object.prototype.hasOwnProperty,ky=function(e,t,r){if(!Iy(t))throw new TypeError("iterator must be a function");var n;arguments.length>=3&&(n=r),"[object Array]"===By.call(e)?function(e,t,r){for(var n=0,o=e.length;n<o;n++)Fy.call(e,n)&&(null==r?t(e[n],n,e):t.call(r,e[n],n,e))}(e,t,n):"string"==typeof e?function(e,t,r){for(var n=0,o=e.length;n<o;n++)null==r?t(e.charAt(n),n,e):t.call(r,e.charAt(n),n,e)}(e,t,n):function(e,t,r){for(var n in e)Fy.call(e,n)&&(null==r?t(e[n],n,e):t.call(r,e[n],n,e))}(e,t,n)},Ny=["BigInt64Array","BigUint64Array","Float32Array","Float64Array","Int16Array","Int32Array","Int8Array","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray"],Ly="undefined"==typeof globalThis?r:globalThis,Uy=function(){for(var e=[],t=0;t<Ny.length;t++)"function"==typeof Ly[Ny[t]]&&(e[e.length]=Ny[t]);return e},Dy=Dp("%Object.getOwnPropertyDescriptor%",!0);if(Dy)try{Dy([],"length")}catch(ae){Dy=null}var Hy=Dy,zy=ky,Wy=Uy,$y=af,Vy=$y("Object.prototype.toString"),Gy=Xf(),Jy=Hy,Qy="undefined"==typeof globalThis?r:globalThis,Xy=Wy(),Ky=$y("Array.prototype.indexOf",!0)||function(e,t){for(var r=0;r<e.length;r+=1)if(e[r]===t)return r;return-1},Yy=$y("String.prototype.slice"),Zy={},ev=Object.getPrototypeOf;Gy&&Jy&&ev&&zy(Xy,(function(e){var t=new Qy[e];if(Symbol.toStringTag in t){var r=ev(t),n=Jy(r,Symbol.toStringTag);if(!n){var o=ev(r);n=Jy(o,Symbol.toStringTag)}Zy[e]=n.get}}));var tv,rv,nv,ov,av=function(e){if(!e||"object"!=typeof e)return!1;if(!Gy||!(Symbol.toStringTag in e)){var t=Yy(Vy(e),8,-1);return Ky(Xy,t)>-1}return!!Jy&&function(e){var t=!1;return zy(Zy,(function(r,n){if(!t)try{t=r.call(e)===n}catch(e){}})),t}(e)},iv=tf.exports,lv=af,uv=Dp,sv=av,cv=uv("ArrayBuffer",!0),dv=uv("Float32Array",!0),pv=lv("ArrayBuffer.prototype.byteLength",!0),fv=cv&&!pv&&(new cv).slice,mv=fv&&iv(fv),bv=pv||mv?function(e){if(!e||"object"!=typeof e)return!1;try{return pv?pv(e):mv(e,0),!0}catch(e){return!1}}:dv?function(e){try{return new dv(e).buffer===e&&!sv(e)}catch(t){return"object"==typeof e&&"RangeError"===t.name}}:function(){return!1},yv=Date.prototype.getDay,vv=Object.prototype.toString,hv=Xf(),gv=af,Pv=Xf();if(Pv){tv=gv("Object.prototype.hasOwnProperty"),rv=gv("RegExp.prototype.exec"),nv={};var Cv=function(){throw nv};ov={toString:Cv,valueOf:Cv},"symbol"==typeof Symbol.toPrimitive&&(ov[Symbol.toPrimitive]=Cv)}var qv=gv("Object.prototype.toString"),wv=Object.getOwnPropertyDescriptor,Ev=Pv?function(e){if(!e||"object"!=typeof e)return!1;var t=wv(e,"lastIndex");if(!(t&&tv(t,"value")))return!1;try{rv(e,ov)}catch(e){return e===nv}}:function(e){return!(!e||"object"!=typeof e&&"function"!=typeof e)&&"[object RegExp]"===qv(e)},xv=af("SharedArrayBuffer.prototype.byteLength",!0),Ov=xv?function(e){if(!e||"object"!=typeof e)return!1;try{return xv(e),!0}catch(e){return!1}}:function(){return!1},jv=Number.prototype.toString,Sv=Object.prototype.toString,Rv=Xf(),Av=af,Tv=Av("Boolean.prototype.toString"),_v=Av("Object.prototype.toString"),Mv=Xf(),Iv={exports:{}},Bv=Object.prototype.toString;if(lp()){var Fv=Symbol.prototype.toString,kv=/^Symbol\(.*\)$/;Iv.exports=function(e){if("symbol"==typeof e)return!0;if("[object Symbol]"!==Bv.call(e))return!1;try{return function(e){return"symbol"==typeof e.valueOf()&&kv.test(Fv.call(e))}(e)}catch(e){return!1}}}else Iv.exports=function(e){return!1};var Nv={exports:{}},Lv="undefined"!=typeof BigInt&&BigInt;if("function"==typeof Lv&&"function"==typeof BigInt&&"bigint"==typeof Lv(42)&&"bigint"==typeof BigInt(42)){var Uv=BigInt.prototype.valueOf;Nv.exports=function(e){return null!=e&&"boolean"!=typeof e&&"string"!=typeof e&&"number"!=typeof e&&"symbol"!=typeof e&&"function"!=typeof e&&("bigint"==typeof e||function(e){try{return Uv.call(e),!0}catch(e){}return!1}(e))}}else Nv.exports=function(e){return!1};var Dv,Hv=Rb,zv=function(e){return"number"==typeof e||"object"==typeof e&&(Rv?function(e){try{return jv.call(e),!0}catch(e){return!1}}(e):"[object Number]"===Sv.call(e))},Wv=function(e){return"boolean"==typeof e||null!==e&&"object"==typeof e&&(Mv&&Symbol.toStringTag in e?function(e){try{return Tv(e),!0}catch(e){return!1}}(e):"[object Boolean]"===_v(e))},$v=Iv.exports,Vv=Nv.exports,Gv="function"==typeof WeakMap&&WeakMap.prototype?WeakMap:null,Jv="function"==typeof WeakSet&&WeakSet.prototype?WeakSet:null;Gv||(Dv=function(){return!1});var Qv=Gv?Gv.prototype.has:null,Xv=Jv?Jv.prototype.has:null;Dv||Qv||(Dv=function(){return!1});var Kv=Dv||function(e){if(!e||"object"!=typeof e)return!1;try{if(Qv.call(e,Qv),Xv)try{Xv.call(e,Xv)}catch(e){return!0}return e instanceof Gv}catch(e){}return!1},Yv={exports:{}},Zv=af,eh=Dp("%WeakSet%",!0),th=Zv("WeakSet.prototype.has",!0);if(th){var rh=Zv("WeakMap.prototype.has",!0);Yv.exports=function(e){if(!e||"object"!=typeof e)return!1;try{if(th(e,th),rh)try{rh(e,rh)}catch(e){return!0}return e instanceof eh}catch(e){}return!1}}else Yv.exports=function(){return!1};var nh=Bb,oh=Ub,ah=Kv,ih=Yv.exports,lh=ky,uh=Uy,sh=af,ch=Hy,dh=sh("Object.prototype.toString"),ph=Xf(),fh="undefined"==typeof globalThis?r:globalThis,mh=uh(),bh=sh("String.prototype.slice"),yh={},vh=Object.getPrototypeOf;ph&&ch&&vh&&lh(mh,(function(e){if("function"==typeof fh[e]){var t=new fh[e];if(Symbol.toStringTag in t){var r=vh(t),n=ch(r,Symbol.toStringTag);if(!n){var o=vh(r);n=ch(o,Symbol.toStringTag)}yh[e]=n.get}}}));var hh=av,gh=af("ArrayBuffer.prototype.byteLength",!0),Ph=bv,Ch=xf,qh=af,wh=Gf,Eh=Dp,xh=Jf.exports,Oh=mb,jh=qy,Sh=rm,Rh=xb,Ah=bv,Th=function(e){return"object"==typeof e&&null!==e&&(hv?function(e){try{return yv.call(e),!0}catch(e){return!1}}(e):"[object Date]"===vv.call(e))},_h=Ev,Mh=Ov,Ih=np,Bh=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e?null:Hv(e)?"String":zv(e)?"Number":Wv(e)?"Boolean":$v(e)?"Symbol":Vv(e)?"BigInt":void 0},Fh=function(e){if(e&&"object"==typeof e){if(nh(e))return"Map";if(oh(e))return"Set";if(ah(e))return"WeakMap";if(ih(e))return"WeakSet"}return!1},kh=function(e){return!!hh(e)&&(ph&&Symbol.toStringTag in e?function(e){var t=!1;return lh(yh,(function(r,n){if(!t)try{var o=r.call(e);o===n&&(t=o)}catch(e){}})),t}(e):bh(dh(e),8,-1))},Nh=function(e){return Ph(e)?gh?gh(e):e.byteLength:NaN},Lh=qh("SharedArrayBuffer.prototype.byteLength",!0),Uh=qh("Date.prototype.getTime"),Dh=Object.getPrototypeOf,Hh=qh("Object.prototype.toString"),zh=Eh("%Set%",!0),Wh=qh("Map.prototype.has",!0),$h=qh("Map.prototype.get",!0),Vh=qh("Map.prototype.size",!0),Gh=qh("Set.prototype.add",!0),Jh=qh("Set.prototype.delete",!0),Qh=qh("Set.prototype.has",!0),Xh=qh("Set.prototype.size",!0);function Kh(e,t,r,n){for(var o,a=xh(e);(o=a.next())&&!o.done;)if(rg(t,o.value,r,n))return Jh(e,o.value),!0;return!1}function Yh(e){return void 0===e?null:"object"!=typeof e?"symbol"!=typeof e&&("string"!=typeof e&&"number"!=typeof e||+e==+e):void 0}function Zh(e,t,r,n,o,a){var i=Yh(r);if(null!=i)return i;var l=$h(t,i),u=Ch({},o,{strict:!1});return!(void 0===l&&!Wh(t,i)||!rg(n,l,u,a))&&(!Wh(e,i)&&rg(n,l,u,a))}function eg(e,t,r){var n=Yh(r);return null!=n?n:Qh(t,n)&&!Qh(e,n)}function tg(e,t,r,n,o,a){for(var i,l,u=xh(e);(i=u.next())&&!i.done;)if(rg(r,l=i.value,o,a)&&rg(n,$h(t,l),o,a))return Jh(e,l),!0;return!1}function rg(e,t,r,n){var o=r||{};if(o.strict?jh(e,t):e===t)return!0;if(Bh(e)!==Bh(t))return!1;if(!e||!t||"object"!=typeof e&&"object"!=typeof t)return o.strict?jh(e,t):e==t;var a,i=n.has(e),l=n.has(t);if(i&&l){if(n.get(e)===n.get(t))return!0}else a={};return i||n.set(e,a),l||n.set(t,a),function(e,t,r,n){var o,a;if(typeof e!=typeof t)return!1;if(null==e||null==t)return!1;if(Hh(e)!==Hh(t))return!1;if(Sh(e)!==Sh(t))return!1;var i=Rh(e),l=Rh(t);if(i!==l)return!1;var u=e instanceof Error,s=t instanceof Error;if(u!==s)return!1;if((u||s)&&(e.name!==t.name||e.message!==t.message))return!1;var c=_h(e),d=_h(t);if(c!==d)return!1;if((c||d)&&(e.source!==t.source||wh(e)!==wh(t)))return!1;var p=Th(e),f=Th(t);if(p!==f)return!1;if((p||f)&&Uh(e)!==Uh(t))return!1;if(r.strict&&Dh&&Dh(e)!==Dh(t))return!1;var m=kh(e),b=kh(t);if((m||b)&&m!==b)return!1;var y=ng(e),v=ng(t);if(y!==v)return!1;if(y||v){if(e.length!==t.length)return!1;for(o=0;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}var h=Ah(e),g=Ah(t);if(h!==g)return!1;if(h||g)return Nh(e)===Nh(t)&&("function"==typeof Uint8Array&&rg(new Uint8Array(e),new Uint8Array(t),r,n));var P=Mh(e),C=Mh(t);if(P!==C)return!1;if(P||C)return Lh(e)===Lh(t)&&("function"==typeof Uint8Array&&rg(new Uint8Array(e),new Uint8Array(t),r,n));if(typeof e!=typeof t)return!1;var q=Ih(e),w=Ih(t);if(q.length!==w.length)return!1;for(q.sort(),w.sort(),o=q.length-1;o>=0;o--)if(q[o]!=w[o])return!1;for(o=q.length-1;o>=0;o--)if(!rg(e[a=q[o]],t[a],r,n))return!1;var E=Fh(e),x=Fh(t);if(E!==x)return!1;if("Set"===E||"Set"===x)return function(e,t,r,n){if(Xh(e)!==Xh(t))return!1;var o,a,i,l=xh(e),u=xh(t);for(;(o=l.next())&&!o.done;)if(o.value&&"object"==typeof o.value)i||(i=new zh),Gh(i,o.value);else if(!Qh(t,o.value)){if(r.strict)return!1;if(!eg(e,t,o.value))return!1;i||(i=new zh),Gh(i,o.value)}if(i){for(;(a=u.next())&&!a.done;)if(a.value&&"object"==typeof a.value){if(!Kh(i,a.value,r.strict,n))return!1}else if(!r.strict&&!Qh(e,a.value)&&!Kh(i,a.value,r.strict,n))return!1;return 0===Xh(i)}return!0}(e,t,r,n);if("Map"===E)return function(e,t,r,n){if(Vh(e)!==Vh(t))return!1;var o,a,i,l,u,s,c=xh(e),d=xh(t);for(;(o=c.next())&&!o.done;)if(l=o.value[0],u=o.value[1],l&&"object"==typeof l)i||(i=new zh),Gh(i,l);else if(void 0===(s=$h(t,l))&&!Wh(t,l)||!rg(u,s,r,n)){if(r.strict)return!1;if(!Zh(e,t,l,u,r,n))return!1;i||(i=new zh),Gh(i,l)}if(i){for(;(a=d.next())&&!a.done;)if(l=a.value[0],s=a.value[1],l&&"object"==typeof l){if(!tg(i,e,l,s,r,n))return!1}else if(!(r.strict||e.has(l)&&rg($h(e,l),s,r,n)||tg(i,e,l,s,Ch({},r,{strict:!1}),n)))return!1;return 0===Xh(i)}return!0}(e,t,r,n);return!0}(e,t,o,n)}function ng(e){return!(!e||"object"!=typeof e||"number"!=typeof e.length)&&("function"==typeof e.copy&&"function"==typeof e.slice&&(!(e.length>0&&"number"!=typeof e[0])&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))))}Object.defineProperty(kd,"__esModule",{value:!0}),kd.default=void 0;var og=lg((function(e,t,r){return rg(e,t,r,Oh())})),ag=lg(pn),ig=lg(_n);function lg(e){return e&&e.__esModule?e:{default:e}}function ug(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||sg(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sg(e,t){if(e){if("string"==typeof e)return cg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?cg(e,t):void 0}}function cg(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var dg=[],pg=ig.default.keys(),fg=0;fg<pg.length;fg++){var mg=pg[fg],bg=ig.default.get(mg);if(bg)for(var yg=[].concat(bg.baseConcepts,bg.relatedConcepts),vg=0;vg<yg.length;vg++){var hg=yg[vg];if("HTML"===hg.module){var gg=hg.concept;gg&&function(){var e=JSON.stringify(gg),t=dg.find((function(t){return JSON.stringify(t[0])===e})),r=void 0;r=t?t[1]:[];for(var n=!0,o=0;o<r.length;o++)if(r[o]===mg){n=!1;break}n&&r.push(mg),dg.push([gg,r])}()}}}var Pg={entries:function(){return dg},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=sg(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(dg);try{for(n.s();!(t=n.n()).done;){var o=ug(t.value,2),a=o[0],i=o[1];e.call(r,i,a,dg)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=dg.find((function(t){return(0,og.default)(e,t[0])}));return t&&t[1]},has:function(e){return!!Pg.get(e)},keys:function(){return dg.map((function(e){return ug(e,1)[0]}))},values:function(){return dg.map((function(e){return ug(e,2)[1]}))}},Cg=(0,ag.default)(Pg,Pg.entries());kd.default=Cg;var qg={};Object.defineProperty(qg,"__esModule",{value:!0}),qg.default=void 0;var wg=xg(pn),Eg=xg(_n);function xg(e){return e&&e.__esModule?e:{default:e}}function Og(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||jg(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jg(e,t){if(e){if("string"==typeof e)return Sg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Sg(e,t):void 0}}function Sg(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Rg=[],Ag=Eg.default.keys(),Tg=function(e){var t=Ag[e],r=Eg.default.get(t);if(r)for(var n=[].concat(r.baseConcepts,r.relatedConcepts),o=0;o<n.length;o++){var a=n[o];if("HTML"===a.module){var i=a.concept;if(i){var l=Rg.find((function(e){return e[0]===t})),u=void 0;(u=l?l[1]:[]).push(i),Rg.push([t,u])}}}},_g=0;_g<Ag.length;_g++)Tg(_g);var Mg={entries:function(){return Rg},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=jg(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Rg);try{for(n.s();!(t=n.n()).done;){var o=Og(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Rg)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Rg.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Mg.get(e)},keys:function(){return Rg.map((function(e){return Og(e,1)[0]}))},values:function(){return Rg.map((function(e){return Og(e,2)[1]}))}},Ig=(0,wg.default)(Mg,Mg.entries());qg.default=Ig,Object.defineProperty(cn,"__esModule",{value:!0});var Bg=cn.roles=Jg=cn.roleElements=cn.elementRoles=cn.dom=cn.aria=void 0,Fg=Dg(dn),kg=Dg(En),Ng=Dg(_n),Lg=Dg(kd),Ug=Dg(qg);function Dg(e){return e&&e.__esModule?e:{default:e}}var Hg=Fg.default;cn.aria=Hg;var zg=kg.default;cn.dom=zg;var Wg=Ng.default;Bg=cn.roles=Wg;var $g=Lg.default,Vg=cn.elementRoles=$g,Gg=Ug.default,Jg=cn.roleElements=Gg;const Qg=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;return-1!==n.indexOf("undefined")?":not(["+t+"])":r?"["+t+'="'+r+'"]':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[o,a]of e.entries())n=[...n,{match:r(o),roles:Array.from(a),specificity:t(o)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(Vg);function Xg(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function Kg(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=Xg}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function Yg(e){for(const{match:t,roles:r}of Qg)if(t(e))return[...r];return[]}function Zg(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===Kg(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):Yg(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function eP(e,t){let{hidden:r,includeDescription:n}=t;const o=Zg(e,{hidden:r});return Object.entries(o).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const o="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+sn(e,{computedStyleSupportsPseudoElements:fr().computedStyleSupportsPseudoElements})+'":\n',r=cr(e.cloneNode(!1));if(n){return""+t+('Description "'+un(e,{computedStyleSupportsPseudoElements:fr().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+o})).join("\n")}function tP(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const rP=qr();function nP(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function oP(e,t,r,n){let{variant:o,name:a}=n,i="";const l={},u=[["Role","TestId"].includes(e)?r:nP(r)];a&&(l.name=nP(a)),"Role"===e&&Kg(t)&&(l.hidden=!0,i="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(l).length>0&&u.push(l);const s=o+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:o,warning:i,toString(){i&&console.warn(i);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function aP(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function iP(e,t,r){var n,o;if(void 0===t&&(t="get"),e.matches(fr().defaultIgnore))return;const a=null!=(n=e.getAttribute("role"))?n:null==(o=Yg(e))?void 0:o[0];if("generic"!==a&&aP("Role",r,a))return oP("Role",e,a,{variant:t,name:sn(e,{computedStyleSupportsPseudoElements:fr().computedStyleSupportsPseudoElements})});const i=hr(document,e).map((e=>e.content)).join(" ");if(aP("LabelText",r,i))return oP("LabelText",e,i,{variant:t});const l=e.getAttribute("placeholder");if(aP("PlaceholderText",r,l))return oP("PlaceholderText",e,l,{variant:t});const u=rP(xr(e));if(aP("Text",r,u))return oP("Text",e,u,{variant:t});if(aP("DisplayValue",r,e.value))return oP("DisplayValue",e,rP(e.value),{variant:t});const s=e.getAttribute("alt");if(aP("AltText",r,s))return oP("AltText",e,s,{variant:t});const c=e.getAttribute("title");if(aP("Title",r,c))return oP("Title",e,c,{variant:t});const d=e.getAttribute(fr().testIdAttribute);return aP("TestId",r,d)?oP("TestId",e,d,{variant:t}):void 0}function lP(e,t){e.stack=t.stack.replace(t.message,e.message)}function uP(e,t){let{container:r=rr(),timeout:n=fr().asyncUtilTimeout,showOriginalStackTrace:o=fr().showOriginalStackTrace,stackTraceError:a,interval:i=50,onTimeout:l=(e=>(e.message=fr().getElementError(e.message,r).message,e)),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let c,d,p,f=!1,m="idle";const b=setTimeout((function(){let e;c?(e=c,o||"TestingLibraryElementError"!==e.name||lP(e,a)):(e=new Error("Timed out in waitFor."),o||lP(e,a)),v(l(e),null)}),n),y=tr();if(y){const{unstable_advanceTimersWrapper:e}=fr();for(g();!f;){if(!tr()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return o||lP(e,a),void s(e)}if(e((()=>{jest.advanceTimersByTime(i)})),g(),f)break;await e((async()=>{await new Promise((e=>{setTimeout(e,0),jest.advanceTimersByTime(0)}))}))}}else{try{or(r)}catch(e){return void s(e)}d=setInterval(h,i);const{MutationObserver:e}=nr(r);p=new e(h),p.observe(r,u),g()}function v(e,r){f=!0,clearTimeout(b),y||(clearInterval(d),p.disconnect()),e?s(e):t(r)}function h(){if(tr()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return o||lP(e,a),s(e)}return g()}function g(){if("pending"!==m)try{const t=function(e){try{return pr._disableExpensiveErrorDiagnostics=!0,e()}finally{pr._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(m="pending",t.then((e=>{m="resolved",v(null,e)}),(e=>{m="rejected",c=e}))):v(null,t)}catch(e){c=e}}}))}function sP(e,t){const r=new Error("STACK_TRACE_MESSAGE");return fr().asyncWrapper((()=>uP(e,{stackTraceError:r,...t})))}function cP(e,t){return fr().getElementError(e,t)}function dP(e,t){return cP(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function pP(e,t,r,n){let{exact:o=!0,collapseWhitespace:a,trim:i,normalizer:l}=void 0===n?{}:n;const u=o?Cr:Pr,s=wr({collapseWhitespace:a,trim:i,normalizer:l});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function fP(e,t,r,n){const o=pP(e,t,r,n);if(o.length>1)throw dP("Found multiple elements by ["+e+"="+r+"]",t);return o[0]||null}function mP(e,t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=e(r,...o);if(i.length>1){const e=i.map((e=>cP(null,e).message)).join("\n\n");throw dP(t(r,...o)+"\n\nHere are the matching elements:\n\n"+e,r)}return i[0]||null}}function bP(e,t){return fr().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function yP(e,t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=e(r,...o);if(!i.length)throw fr().getElementError(t(r,...o),r);return i}}function vP(e){return(t,r,n,o)=>sP((()=>e(t,r,n)),{container:t,...o})}const hP=(e,t,r)=>function(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];const l=e(n,...a),[{suggest:u=fr().throwSuggestions}={}]=a.slice(-1);if(l&&u){const e=iP(l,r);if(e&&!t.endsWith(e.queryName))throw bP(e.toString(),n)}return l},gP=(e,t,r)=>function(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];const l=e(n,...a),[{suggest:u=fr().throwSuggestions}={}]=a.slice(-1);if(l.length&&u){const e=[...new Set(l.map((e=>{var t;return null==(t=iP(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(iP(l[0],r).queryName))throw bP(e[0],n)}return l};function PP(e,t,r){const n=hP(mP(e,t),e.name,"query"),o=yP(e,r),a=mP(o,t),i=hP(a,e.name,"get");return[n,gP(o,e.name.replace("query","get"),"getAll"),i,vP(gP(o,e.name,"findAll")),vP(hP(a,e.name,"find"))]}var CP=Object.freeze({__proto__:null,getElementError:cP,wrapAllByQueryWithSuggestion:gP,wrapSingleQueryWithSuggestion:hP,getMultipleElementsFoundError:dP,queryAllByAttribute:pP,queryByAttribute:fP,makeSingleQuery:mP,makeGetAllQuery:yP,makeFindQuery:vP,buildQueries:PP});const qP=function(e,t,r){let{exact:n=!0,trim:o,collapseWhitespace:a,normalizer:i}=void 0===r?{}:r;const l=n?Cr:Pr,u=wr({collapseWhitespace:a,trim:o,normalizer:i}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:yr(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return l(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},wP=function(e,t,r){let{selector:n="*",exact:o=!0,collapseWhitespace:a,trim:i,normalizer:l}=void 0===r?{}:r;or(e);const u=o?Cr:Pr,s=wr({collapseWhitespace:a,trim:i,normalizer:l}),c=Array.from(e.querySelectorAll("*")).filter((e=>vr(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,o)=>{const a=hr(e,o,{selector:n});a.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const i=a.filter((e=>Boolean(e.content))).map((e=>e.content));return u(i.join(" "),o,t,s)&&r.push(o),i.length>1&&i.forEach(((e,n)=>{u(e,o,t,s)&&r.push(o);const a=[...i];a.splice(n,1),a.length>1&&u(a.join(" "),o,t,s)&&r.push(o)})),r}),[]).concat(pP("aria-label",e,t,{exact:o,normalizer:s}));return Array.from(new Set(c)).filter((e=>e.matches(n)))},EP=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];const a=wP(e,t,...n);if(!a.length){const r=qP(e,t,...n);if(r.length){const n=r.map((t=>function(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}(e,t))).filter((e=>!!e));throw n.length?fr().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):fr().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw fr().getElementError("Unable to find a label with the text of: "+t,e)}return a};const xP=(e,t)=>"Found multiple elements with the text of: "+t,OP=hP(mP(wP,xP),wP.name,"query"),jP=mP(EP,xP),SP=vP(gP(EP,EP.name,"findAll")),RP=vP(hP(jP,EP.name,"find")),AP=gP(EP,EP.name,"getAll"),TP=hP(jP,EP.name,"get"),_P=gP(wP,wP.name,"queryAll"),MP=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return or(t[0]),pP("placeholder",...t)},IP=gP(MP,MP.name,"queryAll"),[BP,FP,kP,NP,LP]=PP(MP,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),UP=function(e,t,r){let{selector:n="*",exact:o=!0,collapseWhitespace:a,trim:i,ignore:l=fr().defaultIgnore,normalizer:u}=void 0===r?{}:r;or(e);const s=o?Cr:Pr,c=wr({collapseWhitespace:a,trim:i,normalizer:u});let d=[];return"function"==typeof e.matches&&e.matches(n)&&(d=[e]),[...d,...Array.from(e.querySelectorAll(n))].filter((e=>!l||!e.matches(l))).filter((e=>s(xr(e),e,t,c)))},DP=gP(UP,UP.name,"queryAll"),[HP,zP,WP,$P,VP]=PP(UP,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:o,normalizer:a,selector:i}=r,l=wr({collapseWhitespace:n,trim:o,normalizer:a})(t.toString());return"Unable to find an element with the text: "+(l!==t.toString()?l+" (normalized from '"+t+"')":t)+("*"!==(null!=i?i:"*")?", which matches selector '"+i+"'":"")+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),GP=function(e,t,r){let{exact:n=!0,collapseWhitespace:o,trim:a,normalizer:i}=void 0===r?{}:r;or(e);const l=n?Cr:Pr,u=wr({collapseWhitespace:o,trim:a,normalizer:i});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>l(xr(e),e,t,u)))}return l(e.value,e,t,u)}))},JP=gP(GP,GP.name,"queryAll"),[QP,XP,KP,YP,ZP]=PP(GP,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),eC=/^(img|input|area|.+-.+)$/i,tC=function(e,t,r){return void 0===r&&(r={}),or(e),pP("alt",e,t,r).filter((e=>eC.test(e.tagName)))},rC=gP(tC,tC.name,"queryAll"),[nC,oC,aC,iC,lC]=PP(tC,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),uC=function(e,t,r){let{exact:n=!0,collapseWhitespace:o,trim:a,normalizer:i}=void 0===r?{}:r;or(e);const l=n?Cr:Pr,u=wr({collapseWhitespace:o,trim:a,normalizer:i});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>l(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&l(xr(e),e,t,u)))},sC=gP(uC,uC.name,"queryAll"),[cC,dC,pC,fC,mC]=PP(uC,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+"."));function bC(e,t,r){let{exact:n=!0,collapseWhitespace:o,hidden:a=fr().defaultHidden,name:i,description:l,trim:u,normalizer:s,queryFallbacks:c=!1,selected:d,checked:p,pressed:f,current:m,level:b,expanded:y}=void 0===r?{}:r;or(e);const v=n?Cr:Pr,h=wr({collapseWhitespace:o,trim:u,normalizer:s});var g,P,C,q,w;if(void 0!==d&&void 0===(null==(g=Bg.get(t))?void 0:g.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==p&&void 0===(null==(P=Bg.get(t))?void 0:P.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==f&&void 0===(null==(C=Bg.get(t))?void 0:C.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==m&&void 0===(null==(q=Bg.get(t))?void 0:q.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==b&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==y&&void 0===(null==(w=Bg.get(t))?void 0:w.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const E=new WeakMap;function x(e){return E.has(e)||E.set(e,Xg(e)),E.get(e)}return Array.from(e.querySelectorAll(function(e,t,r){var n;if("string"!=typeof e)return"*";const o=t&&!r?'*[role~="'+e+'"]':"*[role]",a=null!=(n=Jg.get(e))?n:new Set,i=new Set(Array.from(a).map((e=>{let{name:t}=e;return t})));return[o].concat(Array.from(i)).join(",")}(t,n,s?h:void 0))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(c)return r.split(" ").filter(Boolean).some((r=>v(r,e,t,h)));if(s)return v(r,e,t,h);const[n]=r.split(" ");return v(n,e,t,h)}return Yg(e).some((r=>v(r,e,t,h)))})).filter((e=>void 0!==d?d===function(e){return"OPTION"===e.tagName?e.selected:tP(e,"aria-selected")}(e):void 0!==p?p===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:tP(e,"aria-checked")}(e):void 0!==f?f===function(e){return tP(e,"aria-pressed")}(e):void 0!==m?m===function(e){var t,r;return null!=(t=null!=(r=tP(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e):void 0!==y?y===function(e){return tP(e,"aria-expanded")}(e):void 0===b||b===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e))).filter((e=>void 0===i||Cr(sn(e,{computedStyleSupportsPseudoElements:fr().computedStyleSupportsPseudoElements}),e,i,(e=>e)))).filter((e=>void 0===l||Cr(un(e,{computedStyleSupportsPseudoElements:fr().computedStyleSupportsPseudoElements}),e,l,(e=>e)))).filter((e=>!1!==a||!1===Kg(e,{isSubtreeInaccessible:x})))}const yC=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},vC=gP(bC,bC.name,"queryAll"),[hC,gC,PC,CC,qC]=PP(bC,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+yC(n)}),(function(e,t,r){let{hidden:n=fr().defaultHidden,name:o,description:a}=void 0===r?{}:r;if(fr()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+yC(o);let i,l="";Array.from(e.children).forEach((e=>{l+=eP(e,{hidden:n,includeDescription:void 0!==a})})),i=0===l.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+l.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===o?"":"string"==typeof o?' and name "'+o+'"':" and name `"+o+"`";let s="";return s=void 0===a?"":"string"==typeof a?' and description "'+a+'"':" and description `"+a+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+i).trim()})),wC=()=>fr().testIdAttribute,EC=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return or(t[0]),pP(wC(),...t)},xC=gP(EC,EC.name,"queryAll"),[OC,jC,SC,RC,AC]=PP(EC,((e,t)=>"Found multiple elements by: ["+wC()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+wC()+'="'+t+'"]'));var TC=Object.freeze({__proto__:null,queryAllByLabelText:_P,queryByLabelText:OP,getAllByLabelText:AP,getByLabelText:TP,findAllByLabelText:SP,findByLabelText:RP,queryByPlaceholderText:BP,queryAllByPlaceholderText:IP,getByPlaceholderText:kP,getAllByPlaceholderText:FP,findAllByPlaceholderText:NP,findByPlaceholderText:LP,queryByText:HP,queryAllByText:DP,getByText:WP,getAllByText:zP,findAllByText:$P,findByText:VP,queryByDisplayValue:QP,queryAllByDisplayValue:JP,getByDisplayValue:KP,getAllByDisplayValue:XP,findAllByDisplayValue:YP,findByDisplayValue:ZP,queryByAltText:nC,queryAllByAltText:rC,getByAltText:aC,getAllByAltText:oC,findAllByAltText:iC,findByAltText:lC,queryByTitle:cC,queryAllByTitle:sC,getByTitle:pC,getAllByTitle:dC,findAllByTitle:fC,findByTitle:mC,queryByRole:hC,queryAllByRole:vC,getAllByRole:gC,getByRole:PC,findAllByRole:CC,findByRole:qC,queryByTestId:OC,queryAllByTestId:xC,getByTestId:SC,getAllByTestId:jC,findAllByTestId:RC,findByTestId:AC});function _C(e,t,r){return void 0===t&&(t=TC),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const o=t[n];return r[n]=o.bind(null,e),r}),r)}const MC=e=>!e||Array.isArray(e)&&!e.length;function IC(e){if(MC(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const BC={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}},offline:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},online:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}}},FC={doubleClick:"dblClick"};function kC(e,t){return fr().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function NC(e,t,r,n){let{EventType:o="Event",defaultInit:a={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const i={...a,...r},{target:{value:l,files:u,...s}={}}=i;void 0!==l&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:o}=Object.getOwnPropertyDescriptor(n,"value")||{};if(o&&r!==o)o.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,l),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const c=nr(t),d=c[o]||c.Event;let p;if("function"==typeof d)p=new d(e,i);else{p=c.document.createEvent(o);const{bubbles:t,cancelable:r,detail:n,...a}=i;p.initEvent(e,t,r,n),Object.keys(a).forEach((e=>{p[e]=a[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=i[e];"object"==typeof t&&("function"==typeof c.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new c.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}Object.keys(BC).forEach((e=>{const{EventType:t,defaultInit:r}=BC[e],n=e.toLowerCase();NC[e]=(e,o)=>NC(n,e,o,{EventType:t,defaultInit:r}),kC[e]=(t,r)=>kC(t,NC[e](t,r))})),Object.keys(FC).forEach((e=>{const t=FC[e];kC[e]=function(){return kC[t](...arguments)}}));var LC={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function o(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var a={compressToBase64:function(e){if(null==e)return"";var r=a._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:a._decompress(e.length,32,(function(r){return o(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":a._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:a._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=a.compress(e),r=new Uint8Array(2*t.length),n=0,o=t.length;n<o;n++){var i=t.charCodeAt(n);r[2*n]=i>>>8,r[2*n+1]=i%256}return r},decompressFromUint8Array:function(t){if(null==t)return a.decompress(t);for(var r=new Array(t.length/2),n=0,o=r.length;n<o;n++)r[n]=256*t[2*n]+t[2*n+1];var i=[];return r.forEach((function(t){i.push(e(t))})),a.decompress(i.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":a._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),a._decompress(e.length,32,(function(t){return o(r,e.charAt(t))})))},compress:function(t){return a._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,o,a,i={},l={},u="",s="",c="",d=2,p=3,f=2,m=[],b=0,y=0;for(a=0;a<e.length;a+=1)if(u=e.charAt(a),Object.prototype.hasOwnProperty.call(i,u)||(i[u]=p++,l[u]=!0),s=c+u,Object.prototype.hasOwnProperty.call(i,s))c=s;else{if(Object.prototype.hasOwnProperty.call(l,c)){if(c.charCodeAt(0)<256){for(n=0;n<f;n++)b<<=1,y==t-1?(y=0,m.push(r(b)),b=0):y++;for(o=c.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1}else{for(o=1,n=0;n<f;n++)b=b<<1|o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o=0;for(o=c.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1}0==--d&&(d=Math.pow(2,f),f++),delete l[c]}else for(o=i[c],n=0;n<f;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1;0==--d&&(d=Math.pow(2,f),f++),i[s]=p++,c=String(u)}if(""!==c){if(Object.prototype.hasOwnProperty.call(l,c)){if(c.charCodeAt(0)<256){for(n=0;n<f;n++)b<<=1,y==t-1?(y=0,m.push(r(b)),b=0):y++;for(o=c.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1}else{for(o=1,n=0;n<f;n++)b=b<<1|o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o=0;for(o=c.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1}0==--d&&(d=Math.pow(2,f),f++),delete l[c]}else for(o=i[c],n=0;n<f;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1;0==--d&&(d=Math.pow(2,f),f++)}for(o=2,n=0;n<f;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1;for(;;){if(b<<=1,y==t-1){m.push(r(b));break}y++}return m.join("")},decompress:function(e){return null==e?"":""==e?null:a._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var o,a,i,l,u,s,c,d=[],p=4,f=4,m=3,b="",y=[],v={val:n(0),position:r,index:1};for(o=0;o<3;o+=1)d[o]=o;for(i=0,u=Math.pow(2,2),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;switch(i){case 0:for(i=0,u=Math.pow(2,8),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;c=e(i);break;case 1:for(i=0,u=Math.pow(2,16),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;c=e(i);break;case 2:return""}for(d[3]=c,a=c,y.push(c);;){if(v.index>t)return"";for(i=0,u=Math.pow(2,m),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;switch(c=i){case 0:for(i=0,u=Math.pow(2,8),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;d[f++]=e(i),c=f-1,p--;break;case 1:for(i=0,u=Math.pow(2,16),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;d[f++]=e(i),c=f-1,p--;break;case 2:return y.join("")}if(0==p&&(p=Math.pow(2,m),m++),d[c])b=d[c];else{if(c!==f)return null;b=a+a.charAt(0)}y.push(b),d[f++]=a+b.charAt(0),a=b,0==--p&&(p=Math.pow(2,m),m++)}}};return a}();null!=e?e.exports=t:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",(function(){return t}))}(LC);var UC=LC.exports;function DC(e){return"https://testing-playground.com/#markup="+(t=e,UC.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}const HC={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>dr(e,t,r))):dr(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=rr().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=DC(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},zC="undefined"!=typeof document&&document.body?_C(document.body,TC,HC):Object.keys(TC).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),HC);e.buildQueries=PP,e.configure=function(e){"function"==typeof e&&(e=e(pr)),pr={...pr,...e}},e.createEvent=NC,e.findAllByAltText=iC,e.findAllByDisplayValue=YP,e.findAllByLabelText=SP,e.findAllByPlaceholderText=NP,e.findAllByRole=CC,e.findAllByTestId=RC,e.findAllByText=$P,e.findAllByTitle=fC,e.findByAltText=lC,e.findByDisplayValue=ZP,e.findByLabelText=RP,e.findByPlaceholderText=LP,e.findByRole=qC,e.findByTestId=AC,e.findByText=VP,e.findByTitle=mC,e.fireEvent=kC,e.getAllByAltText=oC,e.getAllByDisplayValue=XP,e.getAllByLabelText=AP,e.getAllByPlaceholderText=FP,e.getAllByRole=gC,e.getAllByTestId=jC,e.getAllByText=zP,e.getAllByTitle=dC,e.getByAltText=aC,e.getByDisplayValue=KP,e.getByLabelText=TP,e.getByPlaceholderText=kP,e.getByRole=PC,e.getByTestId=SC,e.getByText=WP,e.getByTitle=pC,e.getConfig=fr,e.getDefaultNormalizer=qr,e.getElementError=cP,e.getMultipleElementsFoundError=dP,e.getNodeText=xr,e.getQueriesForElement=_C,e.getRoles=Zg,e.getSuggestedQuery=iP,e.isInaccessible=Kg,e.logDOM=dr,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(eP(e,{hidden:r}))},e.makeFindQuery=vP,e.makeGetAllQuery=yP,e.makeSingleQuery=mP,e.prettyDOM=cr,e.prettyFormat=Bt,e.queries=TC,e.queryAllByAltText=rC,e.queryAllByAttribute=pP,e.queryAllByDisplayValue=JP,e.queryAllByLabelText=_P,e.queryAllByPlaceholderText=IP,e.queryAllByRole=vC,e.queryAllByTestId=xC,e.queryAllByText=DP,e.queryAllByTitle=sC,e.queryByAltText=nC,e.queryByAttribute=fP,e.queryByDisplayValue=QP,e.queryByLabelText=OP,e.queryByPlaceholderText=BP,e.queryByRole=hC,e.queryByTestId=OC,e.queryByText=HP,e.queryByTitle=cC,e.queryHelpers=CP,e.screen=zC,e.waitFor=sP,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){IC(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return IC(e()),sP((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!MC(t))throw r}),t)},e.within=_C,e.wrapAllByQueryWithSuggestion=gP,e.wrapSingleQueryWithSuggestion=hP,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=dom.umd.min.js.map
