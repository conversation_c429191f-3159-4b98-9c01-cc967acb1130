// AI Agents and Workflow Management System
class WorkflowManager {
    constructor() {
        this.workflows = new Map();
        this.templates = new Map();
        this.activeExecutions = new Map();
        this.aiAgents = new Map();
        this.currentFilter = 'all';
        this.currentAgentFilter = 'all';
        this.init();
    }

    init() {
        this.loadWorkflowTemplates();
        this.loadActiveWorkflows();
        this.loadAIAgents();
        this.setupEventListeners();
        this.startRealTimeUpdates();
    }

    // AI Agents Management
    async loadAIAgents() {
        try {
            const response = await fetch('http://localhost:8000/api/agents');
            if (response.ok) {
                const data = await response.json();
                data.agents.forEach(agent => {
                    this.aiAgents.set(agent.id, agent);
                });
                this.updateAgentsDisplay();
            } else {
                throw new Error('Server response not ok');
            }
        } catch (error) {
            console.log('Loading mock AI agents...');
            this.loadMockAgents();
        }
    }

    loadMockAgents() {
        const mockAgents = [
            {
                id: 'agent_trading_001',
                name: 'Alpha Trading Agent',
                type: 'trading',
                status: 'active',
                performance_metrics: {
                    total_decisions: 156,
                    successful_decisions: 142,
                    failed_decisions: 14,
                    avg_response_time: 1250,
                    last_activity: Date.now(),
                    profit_loss: 2450.75,
                    trades_executed: 89
                },
                task_queue_size: 3,
                uptime: 86400000,
                created_at: Date.now() - 86400000
            },
            {
                id: 'agent_analysis_001',
                name: 'Market Analyzer Pro',
                type: 'analysis',
                status: 'active',
                performance_metrics: {
                    total_decisions: 234,
                    successful_decisions: 221,
                    failed_decisions: 13,
                    avg_response_time: 890,
                    last_activity: Date.now(),
                    profit_loss: 0,
                    trades_executed: 0
                },
                task_queue_size: 1,
                uptime: 172800000,
                created_at: Date.now() - 172800000
            },
            {
                id: 'agent_optimization_001',
                name: 'Portfolio Optimizer',
                type: 'optimization',
                status: 'active',
                performance_metrics: {
                    total_decisions: 67,
                    successful_decisions: 63,
                    failed_decisions: 4,
                    avg_response_time: 2100,
                    last_activity: Date.now(),
                    profit_loss: 1890.25,
                    trades_executed: 23
                },
                task_queue_size: 0,
                uptime: 259200000,
                created_at: Date.now() - 259200000
            },
            {
                id: 'agent_security_001',
                name: 'Security Guardian',
                type: 'security',
                status: 'active',
                performance_metrics: {
                    total_decisions: 445,
                    successful_decisions: 441,
                    failed_decisions: 4,
                    avg_response_time: 450,
                    last_activity: Date.now(),
                    profit_loss: 0,
                    trades_executed: 0
                },
                task_queue_size: 2,
                uptime: 345600000,
                created_at: Date.now() - 345600000
            }
        ];

        mockAgents.forEach(agent => {
            this.aiAgents.set(agent.id, agent);
        });
        this.updateAgentsDisplay();
    }

    async createAIAgent(agentData) {
        try {
            const response = await fetch('http://localhost:8000/api/agents', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(agentData)
            });

            if (response.ok) {
                const data = await response.json();
                this.aiAgents.set(data.agent.id, data.agent);
                this.updateAgentsDisplay();
                this.showNotification('AI Agent created successfully!', 'success');
                return data.agent;
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Failed to create agent');
            }
        } catch (error) {
            console.error('Error creating AI agent:', error);
            this.showNotification('Failed to create AI agent: ' + error.message, 'error');
            return null;
        }
    }

    async assignTaskToAgent(agentId, taskData) {
        try {
            const response = await fetch(`http://localhost:8000/api/agents/${agentId}/tasks`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(taskData)
            });

            if (response.ok) {
                const data = await response.json();
                this.showNotification('Task assigned successfully!', 'success');
                this.loadAIAgents(); // Refresh agents data
                return data.task;
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Failed to assign task');
            }
        } catch (error) {
            console.error('Error assigning task:', error);
            this.showNotification('Failed to assign task: ' + error.message, 'error');
            return null;
        }
    }

    async coordinateAgents(agentIds, taskType, parameters) {
        try {
            const response = await fetch('http://localhost:8000/api/agents/coordinate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    agent_ids: agentIds,
                    task_type: taskType,
                    parameters
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.showNotification('Agent coordination initiated!', 'success');
                return data.coordination_id;
            } else {
                const error = await response.json();
                throw new Error(error.error || 'Failed to coordinate agents');
            }
        } catch (error) {
            console.error('Error coordinating agents:', error);
            this.showNotification('Failed to coordinate agents: ' + error.message, 'error');
            return null;
        }
    }

    updateAgentsDisplay() {
        const agentsContainer = document.getElementById('ai-agents-container');
        if (!agentsContainer) return;

        const filteredAgents = this.getFilteredAgents();

        agentsContainer.innerHTML = filteredAgents.map(agent => `
            <div class="agent-card" data-agent-id="${agent.id}">
                <div class="agent-header">
                    <div class="agent-info">
                        <h4 class="agent-name">${agent.name}</h4>
                        <span class="agent-type">${agent.type}</span>
                    </div>
                    <div class="agent-status">
                        <span class="status-indicator status-${agent.status}"></span>
                        <span class="status-text">${agent.status}</span>
                    </div>
                </div>

                <div class="agent-metrics">
                    <div class="metric">
                        <span class="metric-label">Success Rate</span>
                        <span class="metric-value">${this.calculateSuccessRate(agent)}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Decisions</span>
                        <span class="metric-value">${agent.performance_metrics.total_decisions}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Queue</span>
                        <span class="metric-value">${agent.task_queue_size}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Uptime</span>
                        <span class="metric-value">${this.formatUptime(agent.uptime)}</span>
                    </div>
                    ${agent.type === 'trading' ? `
                    <div class="metric">
                        <span class="metric-label">P&L</span>
                        <span class="metric-value ${agent.performance_metrics.profit_loss >= 0 ? 'positive' : 'negative'}">
                            $${agent.performance_metrics.profit_loss.toFixed(2)}
                        </span>
                    </div>
                    ` : ''}
                </div>

                <div class="agent-actions">
                    <button class="btn btn-sm btn-primary" onclick="showAssignTaskModal('${agent.id}')">
                        Assign Task
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="viewAgentDetails('${agent.id}')">
                        Details
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="pauseAgent('${agent.id}')">
                        ${agent.status === 'active' ? 'Pause' : 'Resume'}
                    </button>
                </div>
            </div>
        `).join('');

        this.updateAgentStats();
    }

    getFilteredAgents() {
        const agents = Array.from(this.aiAgents.values());
        if (this.currentAgentFilter === 'all') {
            return agents;
        }
        return agents.filter(agent => agent.type === this.currentAgentFilter || agent.status === this.currentAgentFilter);
    }

    calculateSuccessRate(agent) {
        const total = agent.performance_metrics.total_decisions;
        if (total === 0) return 100;
        return ((agent.performance_metrics.successful_decisions / total) * 100).toFixed(1);
    }

    formatUptime(uptime) {
        const hours = Math.floor(uptime / (1000 * 60 * 60));
        const days = Math.floor(hours / 24);
        if (days > 0) {
            return `${days}d ${hours % 24}h`;
        }
        return `${hours}h`;
    }

    updateAgentStats() {
        const agents = Array.from(this.aiAgents.values());
        const activeAgents = agents.filter(a => a.status === 'active').length;
        const totalTasks = agents.reduce((sum, a) => sum + a.task_queue_size, 0);
        const avgSuccessRate = agents.length > 0 ?
            agents.reduce((sum, a) => sum + parseFloat(this.calculateSuccessRate(a)), 0) / agents.length : 0;
        const totalProfit = agents.reduce((sum, a) => sum + (a.performance_metrics.profit_loss || 0), 0);

        this.updateStatElement('active-agents', activeAgents);
        this.updateStatElement('total-tasks', totalTasks);
        this.updateStatElement('avg-success-rate', `${avgSuccessRate.toFixed(1)}%`);
        this.updateStatElement('total-profit', `$${totalProfit.toFixed(2)}`);
    }

    // Workflow Templates Management
    async loadWorkflowTemplates() {
        try {
            const response = await fetch('http://localhost:8000/api/workflows/templates');
            if (response.ok) {
                const data = await response.json();
                data.templates.forEach(template => {
                    this.templates.set(template.id, template);
                });
            } else {
                throw new Error('Server response not ok');
            }
        } catch (error) {
            this.loadMockTemplates();
        }
        this.updateTemplatesDisplay();
    }

    async loadActiveWorkflows() {
        try {
            const response = await fetch('http://localhost:8000/api/workflows');
            if (response.ok) {
                const data = await response.json();
                data.workflows.forEach(workflow => {
                    this.workflows.set(workflow.id, workflow);
                });
            }
        } catch (error) {
            this.loadMockWorkflows();
        }
        this.updateWorkflowsDisplay();
        this.updateStats();
    }

    setupEventListeners() {
        // Workflow filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentFilter = e.target.dataset.filter;
                this.updateWorkflowsDisplay();
            });
        });

        // Agent filter buttons
        document.querySelectorAll('.agent-filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.agent-filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentAgentFilter = e.target.dataset.filter;
                this.updateAgentsDisplay();
            });
        });

        // Real-time updates
        if (window.kontourRealTime) {
            window.kontourRealTime.on('workflow_update', (data) => {
                this.handleWorkflowUpdate(data);
            });
            window.kontourRealTime.on('agent_task_completed', (data) => {
                this.handleAgentTaskUpdate(data);
            });
        }
    }

    startRealTimeUpdates() {
        setInterval(() => {
            this.updateStats();
            this.updateAgentStats();
            this.updatePerformanceMetrics();
        }, 5000);

        setInterval(() => {
            this.loadActiveWorkflows();
            this.loadAIAgents();
        }, 15000);
    }

    updateStatElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
            element.classList.add('metric-update');
            setTimeout(() => element.classList.remove('metric-update'), 1000);
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${this.getNotificationIcon(type)}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add to notifications container
        let container = document.getElementById('notifications-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notifications-container';
            container.className = 'notifications-container';
            document.body.appendChild(container);
        }

        container.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    getNotificationIcon(type) {
        const icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    loadMockTemplates() {
        const mockTemplates = [
            {
                id: 'auto-trading',
                name: 'Automated Trading',
                description: 'AI-powered automated trading with risk management',
                category: 'trading',
                steps: [
                    { name: 'Market Analysis', type: 'ai_analysis' },
                    { name: 'Risk Assessment', type: 'agent_task' },
                    { name: 'Execute Trade', type: 'swap_execution' },
                    { name: 'Update Portfolio', type: 'wallet_operation' }
                ]
            },
            {
                id: 'portfolio-rebalance',
                name: 'Portfolio Rebalancing',
                description: 'Automatic portfolio rebalancing based on target allocations',
                category: 'portfolio',
                steps: [
                    { name: 'Analyze Portfolio', type: 'wallet_operation' },
                    { name: 'Calculate Rebalancing', type: 'ai_analysis' },
                    { name: 'Execute Trades', type: 'agent_task' },
                    { name: 'Verify Results', type: 'agent_task' }
                ]
            },
            {
                id: 'yield-farming',
                name: 'DeFi Yield Farming',
                description: 'Automated yield farming optimization',
                category: 'defi',
                steps: [
                    { name: 'Scan Opportunities', type: 'data_processing' },
                    { name: 'Risk Analysis', type: 'ai_analysis' },
                    { name: 'Optimize Allocation', type: 'agent_task' },
                    { name: 'Execute Farming', type: 'wallet_operation' }
                ]
            }
        ];

        mockTemplates.forEach(template => {
            this.templates.set(template.id, template);
        });
    }

    loadMockWorkflows() {
        const mockWorkflows = [
            {
                id: 'workflow_1',
                name: 'Auto Trading - BTC/USDC',
                templateId: 'auto-trading',
                status: 'running',
                progress: 75,
                createdAt: new Date(Date.now() - 300000).toISOString(),
                startTime: new Date(Date.now() - 300000).toISOString()
            },
            {
                id: 'workflow_2',
                name: 'Portfolio Rebalance',
                templateId: 'portfolio-rebalance',
                status: 'completed',
                progress: 100,
                createdAt: new Date(Date.now() - 3600000).toISOString(),
                startTime: new Date(Date.now() - 3600000).toISOString(),
                endTime: new Date(Date.now() - 3300000).toISOString()
            }
        ];

        mockWorkflows.forEach(workflow => {
            this.workflows.set(workflow.id, workflow);
        });
    }

    updateTemplatesDisplay() {
        document.querySelectorAll('.template-card').forEach(card => {
            const templateId = card.dataset.template;
            if (this.templates.has(templateId)) {
                const template = this.templates.get(templateId);
                this.updateTemplateCard(card, template);
            }
        });
    }

    updateTemplateCard(card, template) {
        const description = card.querySelector('.template-description');
        if (description && template.description) {
            description.textContent = template.description;
        }

        const steps = card.querySelector('.template-steps');
        if (steps && template.steps) {
            steps.innerHTML = template.steps.map((step, index) => `
                <div class="step-indicator">
                    <span class="step">${index + 1}</span>
                    <span class="step-name">${step.name}</span>
                </div>
            `).join('');
        }
    }

    updateWorkflowsDisplay() {
        const workflowsList = document.getElementById('workflows-list');
        if (!workflowsList) return;

        const filteredWorkflows = this.getFilteredWorkflows();
        workflowsList.innerHTML = filteredWorkflows.map(workflow => `
            <div class="workflow-item" data-workflow="${workflow.id}">
                <div class="workflow-info">
                    <div class="workflow-icon">
                        ${this.getWorkflowIcon(workflow.templateId)}
                    </div>
                    <div class="workflow-details">
                        <h4>${workflow.name}</h4>
                        <p>Started: ${new Date(workflow.createdAt).toLocaleString()}</p>
                    </div>
                </div>
                <div class="workflow-status">
                    <span class="status-badge status-${workflow.status}">${workflow.status}</span>
                    <div class="workflow-progress">
                        <div class="progress-fill" style="width: ${workflow.progress || 0}%"></div>
                    </div>
                    <button class="btn btn-sm btn-secondary" onclick="viewWorkflowDetails('${workflow.id}')">
                        View
                    </button>
                </div>
            </div>
        `).join('');
    }

    getFilteredWorkflows() {
        const workflows = Array.from(this.workflows.values());
        if (this.currentFilter === 'all') {
            return workflows;
        }
        return workflows.filter(workflow => workflow.status === this.currentFilter);
    }

    getWorkflowIcon(templateId) {
        const icons = {
            'auto-trading': '📈',
            'portfolio-rebalance': '⚖️',
            'yield-farming': '🌾',
            'agent-coordination': '🤖'
        };
        return icons[templateId] || '🔄';
    }

    updateStats() {
        const workflows = Array.from(this.workflows.values());
        const activeCount = workflows.filter(w => w.status === 'running').length;
        const completedToday = workflows.filter(w => {
            const today = new Date().toDateString();
            return w.status === 'completed' && new Date(w.endTime).toDateString() === today;
        }).length;

        this.updateStatElement('active-workflows', activeCount);
        this.updateStatElement('completed-today', completedToday);
    }

    updatePerformanceMetrics() {
        const throughput = (10 + Math.random() * 10).toFixed(1);
        const avgDuration = (2 + Math.random() * 3).toFixed(1);
        const errorRate = (Math.random() * 3).toFixed(1);

        this.updateStatElement('throughput', throughput);
        this.updateStatElement('avg-duration', avgDuration);
        this.updateStatElement('error-rate', errorRate);
    }

    handleWorkflowUpdate(data) {
        if (this.workflows.has(data.workflowId)) {
            const workflow = this.workflows.get(data.workflowId);
            Object.assign(workflow, data);
            this.updateWorkflowsDisplay();
        }
    }

    handleAgentTaskUpdate(data) {
        this.loadAIAgents(); // Refresh agent data
        this.showNotification(`Agent task completed: ${data.result.task_type}`, 'success');
    }
}

// Global variables
let workflowManager;

// Global functions for AI Agents
function showCreateAgentModal() {
    const modal = document.getElementById('create-agent-modal');
    if (modal) {
        modal.classList.add('active');
    }
}

function closeCreateAgentModal() {
    const modal = document.getElementById('create-agent-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

function createNewAgent() {
    const form = document.getElementById('create-agent-form');
    if (!form) return;

    const formData = new FormData(form);
    const agentData = {
        name: formData.get('agent-name'),
        agent_type: formData.get('agent-type'),
        config: {
            initial_capital: parseFloat(formData.get('initial-capital')) || 10000,
            risk_tolerance: formData.get('risk-tolerance') || 'medium',
            max_position_size: parseFloat(formData.get('max-position-size')) || 0.1,
            stop_loss: parseFloat(formData.get('stop-loss')) || 0.05
        }
    };

    if (workflowManager) {
        workflowManager.createAIAgent(agentData).then(agent => {
            if (agent) {
                closeCreateAgentModal();
                form.reset();
            }
        });
    }
}

function showAssignTaskModal(agentId) {
    const modal = document.getElementById('assign-task-modal');
    if (modal) {
        modal.dataset.agentId = agentId;

        // Update modal title with agent name
        const agent = workflowManager.aiAgents.get(agentId);
        if (agent) {
            const title = modal.querySelector('.modal-title');
            if (title) {
                title.textContent = `Assign Task to ${agent.name}`;
            }
        }

        modal.classList.add('active');
    }
}

function closeAssignTaskModal() {
    const modal = document.getElementById('assign-task-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

function assignTaskToAgent() {
    const modal = document.getElementById('assign-task-modal');
    const agentId = modal.dataset.agentId;

    const form = document.getElementById('assign-task-form');
    if (!form || !agentId) return;

    const formData = new FormData(form);
    const taskData = {
        task_type: formData.get('task-type'),
        priority: formData.get('priority') || 'medium',
        parameters: {
            pair: formData.get('trading-pair'),
            amount: parseFloat(formData.get('amount')),
            strategy: formData.get('strategy'),
            description: formData.get('description')
        }
    };

    if (workflowManager) {
        workflowManager.assignTaskToAgent(agentId, taskData).then(task => {
            if (task) {
                closeAssignTaskModal();
                form.reset();
            }
        });
    }
}

function viewAgentDetails(agentId) {
    const agent = workflowManager.aiAgents.get(agentId);
    if (!agent) return;

    const modal = document.getElementById('agent-details-modal');
    if (modal) {
        // Update modal content with agent details
        const content = modal.querySelector('.agent-details-content');
        if (content) {
            content.innerHTML = `
                <div class="agent-detail-header">
                    <h3>${agent.name}</h3>
                    <span class="agent-type-badge">${agent.type}</span>
                </div>

                <div class="agent-detail-metrics">
                    <div class="metric-grid">
                        <div class="metric-item">
                            <label>Status</label>
                            <span class="status-${agent.status}">${agent.status}</span>
                        </div>
                        <div class="metric-item">
                            <label>Success Rate</label>
                            <span>${workflowManager.calculateSuccessRate(agent)}%</span>
                        </div>
                        <div class="metric-item">
                            <label>Total Decisions</label>
                            <span>${agent.performance_metrics.total_decisions}</span>
                        </div>
                        <div class="metric-item">
                            <label>Avg Response Time</label>
                            <span>${agent.performance_metrics.avg_response_time}ms</span>
                        </div>
                        <div class="metric-item">
                            <label>Queue Size</label>
                            <span>${agent.task_queue_size}</span>
                        </div>
                        <div class="metric-item">
                            <label>Uptime</label>
                            <span>${workflowManager.formatUptime(agent.uptime)}</span>
                        </div>
                        ${agent.type === 'trading' ? `
                        <div class="metric-item">
                            <label>P&L</label>
                            <span class="${agent.performance_metrics.profit_loss >= 0 ? 'positive' : 'negative'}">
                                $${agent.performance_metrics.profit_loss.toFixed(2)}
                            </span>
                        </div>
                        <div class="metric-item">
                            <label>Trades Executed</label>
                            <span>${agent.performance_metrics.trades_executed}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>

                <div class="agent-detail-actions">
                    <button class="btn btn-primary" onclick="showAssignTaskModal('${agentId}')">
                        Assign Task
                    </button>
                    <button class="btn btn-secondary" onclick="pauseAgent('${agentId}')">
                        ${agent.status === 'active' ? 'Pause' : 'Resume'}
                    </button>
                    <button class="btn btn-outline" onclick="closeAgentDetailsModal()">
                        Close
                    </button>
                </div>
            `;
        }

        modal.classList.add('active');
    }
}

function closeAgentDetailsModal() {
    const modal = document.getElementById('agent-details-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

function pauseAgent(agentId) {
    const agent = workflowManager.aiAgents.get(agentId);
    if (agent) {
        agent.status = agent.status === 'active' ? 'paused' : 'active';
        workflowManager.updateAgentsDisplay();
        workflowManager.showNotification(
            `Agent ${agent.name} ${agent.status === 'active' ? 'resumed' : 'paused'}`,
            'info'
        );
    }
}

function showCoordinationModal() {
    const modal = document.getElementById('coordination-modal');
    if (modal) {
        // Populate agent selection
        const agentSelect = modal.querySelector('#coordination-agents');
        if (agentSelect) {
            const agents = Array.from(workflowManager.aiAgents.values());
            agentSelect.innerHTML = agents.map(agent => `
                <option value="${agent.id}">${agent.name} (${agent.type})</option>
            `).join('');
        }

        modal.classList.add('active');
    }
}

function closeCoordinationModal() {
    const modal = document.getElementById('coordination-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

function initiateCoordination() {
    const form = document.getElementById('coordination-form');
    if (!form) return;

    const formData = new FormData(form);
    const selectedAgents = Array.from(form.querySelectorAll('#coordination-agents option:checked'))
        .map(option => option.value);

    if (selectedAgents.length < 2) {
        workflowManager.showNotification('Please select at least 2 agents for coordination', 'warning');
        return;
    }

    const taskType = formData.get('coordination-task');
    const parameters = {
        description: formData.get('coordination-description'),
        priority: formData.get('coordination-priority') || 'medium'
    };

    if (workflowManager) {
        workflowManager.coordinateAgents(selectedAgents, taskType, parameters).then(coordinationId => {
            if (coordinationId) {
                closeCoordinationModal();
                form.reset();
            }
        });
    }
}

// Workflow functions
function previewTemplate(templateId) {
    workflowManager.showNotification('Template preview coming soon!', 'info');
}

function executeTemplate(templateId) {
    const modal = document.getElementById('workflow-execution-modal');
    if (modal) {
        const template = workflowManager.templates.get(templateId);
        if (template) {
            document.getElementById('workflow-name').textContent = template.name;
            document.getElementById('workflow-description').textContent = template.description;

            const parametersContainer = document.getElementById('parameter-inputs');
            parametersContainer.innerHTML = getTemplateParameters(templateId);
        }
        modal.classList.add('active');
        modal.dataset.templateId = templateId;
    }
}

function getTemplateParameters(templateId) {
    const parameters = {
        'auto-trading': `
            <div class="parameter-input">
                <label>Trading Pair</label>
                <select name="trading-pair">
                    <option value="BTC/USDC">BTC/USDC</option>
                    <option value="ETH/USDC">ETH/USDC</option>
                    <option value="KONTOUR/USDC">KONTOUR/USDC</option>
                </select>
            </div>
            <div class="parameter-input">
                <label>Investment Amount (USDC)</label>
                <input type="number" name="amount" value="1000" min="100" max="10000">
            </div>
            <div class="parameter-input">
                <label>Risk Level</label>
                <select name="risk-level">
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                </select>
            </div>
        `,
        'portfolio-rebalance': `
            <div class="parameter-input">
                <label>Target BTC Allocation (%)</label>
                <input type="number" name="btc-allocation" value="40" min="0" max="100">
            </div>
            <div class="parameter-input">
                <label>Target ETH Allocation (%)</label>
                <input type="number" name="eth-allocation" value="30" min="0" max="100">
            </div>
            <div class="parameter-input">
                <label>Target KONTOUR Allocation (%)</label>
                <input type="number" name="kontour-allocation" value="20" min="0" max="100">
            </div>
        `,
        'yield-farming': `
            <div class="parameter-input">
                <label>Farming Strategy</label>
                <select name="strategy">
                    <option value="conservative">Conservative (Low Risk)</option>
                    <option value="balanced">Balanced (Medium Risk)</option>
                    <option value="aggressive">Aggressive (High Risk)</option>
                </select>
            </div>
            <div class="parameter-input">
                <label>Minimum APY (%)</label>
                <input type="number" name="min-apy" value="10" min="1" max="100">
            </div>
        `
    };
    return parameters[templateId] || '<p>No parameters required for this workflow.</p>';
}

function closeWorkflowModal() {
    const modal = document.getElementById('workflow-execution-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

function confirmWorkflowExecution() {
    const modal = document.getElementById('workflow-execution-modal');
    const templateId = modal.dataset.templateId;

    if (templateId && workflowManager) {
        const parameters = {};
        modal.querySelectorAll('.parameter-input input, .parameter-input select').forEach(input => {
            parameters[input.name] = input.value;
        });

        workflowManager.showNotification('Workflow execution started!', 'success');
        closeWorkflowModal();
    }
}

function viewWorkflowDetails(workflowId) {
    workflowManager.showNotification('Workflow details coming soon!', 'info');
}

function createCustomWorkflow() {
    workflowManager.showNotification('Custom workflow builder coming soon!', 'info');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('workflow-dashboard') || document.getElementById('ai-agents-container')) {
        workflowManager = new WorkflowManager();
        window.workflowManager = workflowManager;
    }
});