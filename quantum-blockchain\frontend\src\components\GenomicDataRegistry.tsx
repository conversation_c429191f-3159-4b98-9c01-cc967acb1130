import React, { useState, useEffect } from 'react';
import * as ethers from 'ethers';

// Mock ABI for development
const GenomicDataRegistryABI = [
  "function datasetCount() view returns (uint256)",
  "function datasets(uint256) view returns (address owner, string ipfsHash, string metadata, uint256 price, bool active, uint256 timestamp)",
  "function registerData(string ipfsHash, string metadata, uint256 price) external",
  "function requestAccess(uint256 dataId) external",
  "event DataRegistered(uint256 indexed dataId, address indexed owner, string ipfsHash)",
  "event AccessRequested(uint256 indexed dataId, uint256 indexed reqId, address indexed user)"
];

// Types
interface Dataset {
  id: number;
  owner: string;
  ipfsHash: string;
  metadata: string;
  price: string;
  active: boolean;
  timestamp: number;
  summary?: string;
  summaryCID?: string;
}

interface AccessRequest {
  id: number;
  dataId: number;
  requester: string;
  granted: boolean;
  timestamp: number;
  assessment?: string;
  assessmentCID?: string;
}

interface GenomicDataRegistryProps {
  contractAddress: string;
  provider: ethers.BrowserProvider | null;
  account: string | null;
}

const GenomicDataRegistry: React.FC<GenomicDataRegistryProps> = ({
  contractAddress,
  provider,
  account
}) => {
  // State
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [myRequests, setMyRequests] = useState<AccessRequest[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [ipfsHash, setIpfsHash] = useState<string>('');
  const [metadata, setMetadata] = useState<string>('');
  const [price, setPrice] = useState<string>('0');
  const [selectedDatasetId, setSelectedDatasetId] = useState<number | null>(null);
  
  // Contract instance
  const [contract, setContract] = useState<ethers.Contract | null>(null);
  
  // Initialize contract
  useEffect(() => {
    if (provider && contractAddress) {
      try {
        const signer = provider.getSigner();
        const contractInstance = new ethers.Contract(
          contractAddress,
          GenomicDataRegistryABI,
          signer
        );
        setContract(contractInstance);
        setError(null);
      } catch (err) {
        console.error('Failed to initialize contract:', err);
        setError('Failed to initialize contract');
      }
    }
  }, [provider, contractAddress]);
  
  // Load datasets
  useEffect(() => {
    const loadDatasets = async () => {
      if (!contract) return;
      
      try {
        setLoading(true);
        
        // Get dataset count
        const count = await contract.datasetCount();
        const datasetCount = Number(count);
        
        // Load all datasets
        const loadedDatasets: Dataset[] = [];
        for (let i = 0; i < datasetCount; i++) {
          const dataset = await contract.datasets(i);
          
          loadedDatasets.push({
            id: i,
            owner: dataset.owner,
            ipfsHash: dataset.ipfsHash,
            metadata: dataset.metadata,
            price: ethers.formatEther(dataset.price),
            active: dataset.active,
            timestamp: Number(dataset.timestamp)
          });
        }
        
        setDatasets(loadedDatasets);
        setLoading(false);
      } catch (err) {
        console.error('Failed to load datasets:', err);
        setError('Failed to load datasets');
        setLoading(false);
      }
    };
    
    loadDatasets();
  }, [contract]);
  
  // Register new dataset
  const handleRegisterData = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!contract || !account) {
      setError('Please connect your wallet');
      return;
    }
    
    try {
      setLoading(true);
      
      // Convert price to wei
      const priceInWei = ethers.parseEther(price);
      
      // Call contract
      const tx = await contract.registerData(ipfsHash, metadata, priceInWei);
      await tx.wait();
      
      // Reset form
      setIpfsHash('');
      setMetadata('');
      setPrice('0');
      
      setLoading(false);
      alert('Dataset registered successfully!');
    } catch (err) {
      console.error('Failed to register dataset:', err);
      setError('Failed to register dataset');
      setLoading(false);
    }
  };
  
  // Request access to dataset
  const handleRequestAccess = async (dataId: number) => {
    if (!contract || !account) {
      setError('Please connect your wallet');
      return;
    }
    
    try {
      setLoading(true);
      
      // Call contract
      const tx = await contract.requestAccess(dataId);
      await tx.wait();
      
      setLoading(false);
      alert('Access requested successfully!');
    } catch (err) {
      console.error('Failed to request access:', err);
      setError('Failed to request access');
      setLoading(false);
    }
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };
  
  // Truncate address
  const truncateAddress = (address: string) => {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Genomic Data Registry</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* Register Dataset Form */}
      <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <h2 className="text-xl font-bold mb-4">Register New Dataset</h2>
        <form onSubmit={handleRegisterData}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="ipfsHash">
              IPFS Hash
            </label>
            <input
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="ipfsHash"
              type="text"
              placeholder="QmYourIPFSHash..."
              value={ipfsHash}
              onChange={(e) => setIpfsHash(e.target.value)}
              required
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="metadata">
              Metadata
            </label>
            <textarea
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="metadata"
              placeholder="Species: Human, Type: Exome, Format: BAM..."
              value={metadata}
              onChange={(e) => setMetadata(e.target.value)}
              required
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="price">
              Price (ETH)
            </label>
            <input
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="price"
              type="number"
              step="0.001"
              min="0"
              placeholder="0.1"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              required
            />
          </div>
          <div className="flex items-center justify-between">
            <button
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              type="submit"
              disabled={loading}
            >
              {loading ? 'Processing...' : 'Register Dataset'}
            </button>
          </div>
        </form>
      </div>
      
      {/* Datasets List */}
      <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <h2 className="text-xl font-bold mb-4">Available Datasets</h2>
        {loading ? (
          <p>Loading datasets...</p>
        ) : datasets.length === 0 ? (
          <p>No datasets available</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Owner
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    IPFS Hash
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Metadata
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {datasets.map((dataset) => (
                  <tr key={dataset.id}>
                    <td className="py-2 px-4 border-b border-gray-200">{dataset.id}</td>
                    <td className="py-2 px-4 border-b border-gray-200">{truncateAddress(dataset.owner)}</td>
                    <td className="py-2 px-4 border-b border-gray-200">{dataset.ipfsHash.substring(0, 10)}...</td>
                    <td className="py-2 px-4 border-b border-gray-200">{dataset.metadata.substring(0, 20)}...</td>
                    <td className="py-2 px-4 border-b border-gray-200">{dataset.price} ETH</td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <button
                        className="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs"
                        onClick={() => handleRequestAccess(dataset.id)}
                        disabled={loading || dataset.owner.toLowerCase() === account?.toLowerCase()}
                      >
                        Request Access
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default GenomicDataRegistry;
