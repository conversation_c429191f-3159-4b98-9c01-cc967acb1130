import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON>H<PERSON>er, Card<PERSON><PERSON>le, CardContent } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Progress } from '../components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  Brain, 
  Zap, 
  Activity, 
  TrendingUp, 
  Users, 
  Server,
  Database,
  Cpu,
  Network,
  AlertTriangle,
  CheckCircle,
  Clock,
  Play,
  Pause,
  Square,
  RefreshCw
} from 'lucide-react';

interface WorkflowMetrics {
  totalWorkflows: number;
  activeWorkflows: number;
  completedToday: number;
  failureRate: number;
  averageExecutionTime: number;
  aiTokensUsed: number;
}

interface SystemHealth {
  cpu: number;
  memory: number;
  network: number;
  storage: number;
  uptime: number;
  connectedClients: number;
}

interface RealtimeEvent {
  id: string;
  type: string;
  priority: string;
  data: any;
  timestamp: string;
  source: string;
  processed: boolean;
}

interface AIProvider {
  name: string;
  status: 'online' | 'offline' | 'degraded';
  responseTime: number;
  requestsToday: number;
  successRate: number;
}

const EnhancedWorkflowDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<WorkflowMetrics>({
    totalWorkflows: 0,
    activeWorkflows: 0,
    completedToday: 0,
    failureRate: 0,
    averageExecutionTime: 0,
    aiTokensUsed: 0
  });

  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    cpu: 0,
    memory: 0,
    network: 0,
    storage: 0,
    uptime: 0,
    connectedClients: 0
  });

  const [realtimeEvents, setRealtimeEvents] = useState<RealtimeEvent[]>([]);
  const [aiProviders, setAiProviders] = useState<AIProvider[]>([
    { name: 'ChatGPT', status: 'online', responseTime: 1200, requestsToday: 245, successRate: 98.5 },
    { name: 'Claude', status: 'online', responseTime: 950, requestsToday: 189, successRate: 99.2 },
    { name: 'Gemini', status: 'online', responseTime: 800, requestsToday: 156, successRate: 97.8 },
    { name: 'Deepseek', status: 'degraded', responseTime: 2100, requestsToday: 78, successRate: 94.1 }
  ]);

  const [isConnected, setIsConnected] = useState(false);
  const [ws, setWs] = useState<WebSocket | null>(null);

  // WebSocket connection for real-time updates
  useEffect(() => {
    const connectWebSocket = () => {
      try {
        const websocket = new WebSocket('ws://localhost:3001/ws');
        
        websocket.onopen = () => {
          console.log('Dashboard WebSocket connected');
          setIsConnected(true);
          setWs(websocket);
          
          // Subscribe to all event types
          websocket.send(JSON.stringify({
            type: 'subscribe',
            event_types: ['workflow_update', 'ai_response', 'system_alert', 'blockchain_event'],
            filters: {}
          }));
        };
        
        websocket.onmessage = (event) => {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        };
        
        websocket.onclose = () => {
          console.log('Dashboard WebSocket disconnected');
          setIsConnected(false);
          setWs(null);
          setTimeout(connectWebSocket, 3000);
        };
        
        websocket.onerror = (error) => {
          console.error('Dashboard WebSocket error:', error);
          setIsConnected(false);
        };
      } catch (error) {
        console.error('Failed to connect Dashboard WebSocket:', error);
        setTimeout(connectWebSocket, 3000);
      }
    };

    connectWebSocket();

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  const handleWebSocketMessage = useCallback((message: any) => {
    switch (message.type) {
      case 'realtime_event':
        handleRealtimeEvent(message.event);
        break;
      case 'workflow_update':
        updateWorkflowMetrics(message);
        break;
      case 'system_health_update':
        setSystemHealth(message.data);
        break;
      case 'ai_metrics_update':
        updateAIMetrics(message.data);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }, []);

  const handleRealtimeEvent = useCallback((event: RealtimeEvent) => {
    setRealtimeEvents(prev => [event, ...prev.slice(0, 49)]); // Keep last 50 events
  }, []);

  const updateWorkflowMetrics = useCallback((data: any) => {
    // Update workflow metrics based on real-time data
    setMetrics(prev => ({
      ...prev,
      activeWorkflows: data.active_workflows || prev.activeWorkflows,
      totalWorkflows: data.total_workflows || prev.totalWorkflows
    }));
  }, []);

  const updateAIMetrics = useCallback((data: any) => {
    // Update AI provider metrics
    setAiProviders(prev => prev.map(provider => {
      const providerData = data[provider.name.toLowerCase()];
      if (providerData) {
        return {
          ...provider,
          responseTime: providerData.response_time || provider.responseTime,
          requestsToday: providerData.requests_today || provider.requestsToday,
          successRate: providerData.success_rate || provider.successRate,
          status: providerData.status || provider.status
        };
      }
      return provider;
    }));
  }, []);

  // Fetch initial data
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        // Fetch workflow metrics
        const workflowResponse = await fetch('http://localhost:3001/api/workflows');
        if (workflowResponse.ok) {
          const workflowData = await workflowResponse.json();
          setMetrics(prev => ({
            ...prev,
            totalWorkflows: workflowData.total || 0,
            activeWorkflows: workflowData.workflows?.filter((w: any) => w.status === 'running').length || 0
          }));
        }

        // Fetch system health
        const healthResponse = await fetch('http://localhost:3001/api/system/health');
        if (healthResponse.ok) {
          const healthData = await healthResponse.json();
          setSystemHealth({
            cpu: Math.random() * 100,
            memory: (healthData.memory?.heapUsed / healthData.memory?.heapTotal) * 100 || 0,
            network: Math.random() * 100,
            storage: Math.random() * 100,
            uptime: healthData.uptime || 0,
            connectedClients: healthData.connectedClients || 0
          });
        }

        // Fetch AI metrics
        const aiResponse = await fetch('http://localhost:3001/api/ai/metrics');
        if (aiResponse.ok) {
          const aiData = await aiResponse.json();
          setMetrics(prev => ({
            ...prev,
            aiTokensUsed: aiData.tokensUsed || 0,
            averageExecutionTime: aiData.averageResponseTime || 0
          }));
        }
      } catch (error) {
        console.error('Error fetching initial data:', error);
      }
    };

    fetchInitialData();
    const interval = setInterval(fetchInitialData, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-500';
      case 'degraded': return 'text-yellow-500';
      case 'offline': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'degraded': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'offline': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="h-8 w-8 text-purple-600" />
          <h1 className="text-3xl font-bold text-gray-900">Enhanced Workflow Dashboard</h1>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Real-time Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Workflows</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.totalWorkflows}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Workflows</p>
                <p className="text-2xl font-bold text-green-600">{metrics.activeWorkflows}</p>
              </div>
              <Zap className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AI Tokens Used</p>
                <p className="text-2xl font-bold text-purple-600">{metrics.aiTokensUsed.toLocaleString()}</p>
              </div>
              <Brain className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Connected Clients</p>
                <p className="text-2xl font-bold text-orange-600">{systemHealth.connectedClients}</p>
              </div>
              <Users className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="ai-providers">AI Providers</TabsTrigger>
          <TabsTrigger value="system-health">System Health</TabsTrigger>
          <TabsTrigger value="realtime-events">Real-time Events</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* System Health Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Server className="h-5 w-5" />
                  <span>System Resources</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>CPU Usage</span>
                    <span>{systemHealth.cpu.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemHealth.cpu} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Memory Usage</span>
                    <span>{systemHealth.memory.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemHealth.memory} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Network I/O</span>
                    <span>{systemHealth.network.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemHealth.network} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Storage</span>
                    <span>{systemHealth.storage.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemHealth.storage} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Performance Metrics</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Uptime</span>
                  <span className="text-sm font-medium">
                    {Math.floor(systemHealth.uptime / 3600)}h {Math.floor((systemHealth.uptime % 3600) / 60)}m
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Avg Response Time</span>
                  <span className="text-sm font-medium">{metrics.averageExecutionTime.toFixed(0)}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Success Rate</span>
                  <span className="text-sm font-medium text-green-600">
                    {((1 - metrics.failureRate) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Completed Today</span>
                  <span className="text-sm font-medium">{metrics.completedToday}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ai-providers" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {aiProviders.map((provider) => (
              <Card key={provider.name}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{provider.name}</span>
                    {getStatusIcon(provider.status)}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Status</span>
                    <span className={`text-sm font-medium capitalize ${getStatusColor(provider.status)}`}>
                      {provider.status}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Response Time</span>
                    <span className="text-sm font-medium">{provider.responseTime}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Requests Today</span>
                    <span className="text-sm font-medium">{provider.requestsToday}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Success Rate</span>
                    <span className="text-sm font-medium text-green-600">{provider.successRate}%</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="system-health" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Detailed System Health</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Detailed system health monitoring would go here...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="realtime-events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Real-time Events</span>
                <Badge variant="secondary">{realtimeEvents.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {realtimeEvents.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No recent events</p>
                ) : (
                  realtimeEvents.map((event) => (
                    <div key={event.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className={`w-2 h-2 rounded-full ${getPriorityColor(event.priority)}`} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{event.type}</span>
                          <span className="text-xs text-gray-500">
                            {new Date(event.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-xs text-gray-600">From: {event.source}</p>
                      </div>
                      <Badge variant={event.processed ? "success" : "secondary"}>
                        {event.processed ? "Processed" : "Pending"}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedWorkflowDashboard;
