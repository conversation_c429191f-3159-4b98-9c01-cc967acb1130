<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>Advanced Analytics | Kontour Coin 💎</title>
    <meta name="description" content="Comprehensive cryptocurrency analytics with AI-powered insights, real-time market data, and advanced trading metrics.">
    <meta name="keywords" content="cryptocurrency analytics, market analysis, trading metrics, AI insights, blockchain analytics, DeFi analytics">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles/advanced-professional.css" as="style">
    <link rel="preload" href="scripts/advanced-main.js" as="script">
    
    <!-- External resources -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link href="styles/advanced-professional.css" rel="stylesheet">
    
    <!-- Favicons -->
    <link rel="icon" type="image/svg+xml" href="assets/kontour-logo.svg">
    
    <style>
        .analytics-page {
            padding-top: 80px;
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        .analytics-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: var(--space-8) 0;
            margin-bottom: var(--space-8);
        }
        
        .metrics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }
        
        .metric-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
            transition: var(--transition-normal);
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }
        
        .metric-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-4);
        }
        
        .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: var(--text-xl);
        }
        
        .metric-value {
            font-size: var(--text-3xl);
            font-weight: var(--font-extrabold);
            color: var(--gray-900);
            margin-bottom: var(--space-2);
        }
        
        .metric-label {
            color: var(--gray-600);
            font-size: var(--text-sm);
            margin-bottom: var(--space-2);
        }
        
        .metric-change {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
        }
        
        .change-positive {
            color: var(--success);
        }
        
        .change-negative {
            color: var(--error);
        }
        
        .analytics-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--space-8);
            margin-bottom: var(--space-8);
        }
        
        .chart-section {
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
        }
        
        .chart-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chart-tabs {
            display: flex;
            gap: var(--space-2);
        }
        
        .chart-tab {
            padding: var(--space-2) var(--space-4);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            background: none;
            cursor: pointer;
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--gray-600);
            transition: var(--transition-fast);
        }
        
        .chart-tab.active {
            background: var(--primary-purple);
            color: var(--white);
            border-color: var(--primary-purple);
        }
        
        .chart-container {
            padding: var(--space-6);
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-500);
        }
        
        .insights-panel {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }
        
        .insight-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
        }
        
        .insight-header {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }
        
        .insight-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
        }
        
        .insight-content {
            color: var(--gray-700);
            line-height: 1.6;
        }
        
        .market-sentiment {
            background: var(--gradient-primary);
            color: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            text-align: center;
        }
        
        .sentiment-score {
            font-size: var(--text-4xl);
            font-weight: var(--font-extrabold);
            margin-bottom: var(--space-2);
        }
        
        .sentiment-gauge {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-full);
            margin: var(--space-4) 0;
            position: relative;
            overflow: hidden;
        }
        
        .sentiment-fill {
            height: 100%;
            background: var(--white);
            border-radius: var(--radius-full);
            width: 75%;
            transition: var(--transition-normal);
        }
        
        .top-movers {
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
        }
        
        .mover-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--gray-100);
        }
        
        .mover-item:last-child {
            border-bottom: none;
        }
        
        .mover-info {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .mover-icon {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            background: var(--gradient-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-weight: var(--font-bold);
            font-size: var(--text-sm);
        }
        
        .mover-details h5 {
            margin: 0 0 var(--space-1) 0;
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
        }
        
        .mover-details p {
            margin: 0;
            font-size: var(--text-xs);
            color: var(--gray-500);
        }
        
        .mover-change {
            text-align: right;
            font-weight: var(--font-semibold);
        }
        
        @media (max-width: 768px) {
            .analytics-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="navbar-container">
                <a href="index.html" class="navbar-brand">
                    <img src="assets/kontour-logo.svg" alt="Kontour Coin" class="navbar-logo">
                    <span>Kontour Coin</span>
                </a>
                
                <ul class="navbar-nav">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="trading.html">Trading</a></li>
                    <li><a href="wallet.html">Wallet</a></li>
                    <li><a href="analytics.html" class="active">Analytics</a></li>
                    <li><a href="ai-agents.html">AI Agents</a></li>
                </ul>
                
                <div class="navbar-actions">
                    <a href="#export" class="btn btn-outline btn-sm">Export Data</a>
                    <a href="#alerts" class="btn btn-primary btn-sm">Set Alerts</a>
                </div>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Analytics Page -->
    <div class="analytics-page">
        <!-- Analytics Header -->
        <div class="analytics-header">
            <div class="container">
                <h1>Advanced Analytics Dashboard</h1>
                <p>Comprehensive market analysis powered by AI and real-time data processing</p>
            </div>
        </div>

        <div class="container">
            <!-- Metrics Overview -->
            <div class="metrics-overview">
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" style="background: var(--success);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="metric-change change-positive">
                            <i class="fas fa-arrow-up"></i>
                            +12.4%
                        </div>
                    </div>
                    <div class="metric-value" id="totalMarketCap">$2.4T</div>
                    <div class="metric-label">Total Market Cap</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" style="background: var(--info);">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="metric-change change-positive">
                            <i class="fas fa-arrow-up"></i>
                            +8.7%
                        </div>
                    </div>
                    <div class="metric-value" id="volume24h">$156.7B</div>
                    <div class="metric-label">24h Trading Volume</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" style="background: var(--warning);">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="metric-change change-negative">
                            <i class="fas fa-arrow-down"></i>
                            -2.1%
                        </div>
                    </div>
                    <div class="metric-value" id="btcDominance">42.3%</div>
                    <div class="metric-label">BTC Dominance</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" style="background: var(--primary-purple);">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="metric-change change-positive">
                            <i class="fas fa-arrow-up"></i>
                            +15.2%
                        </div>
                    </div>
                    <div class="metric-value" id="fearGreedIndex">67</div>
                    <div class="metric-label">Fear & Greed Index</div>
                </div>
            </div>

            <!-- Main Analytics Grid -->
            <div class="analytics-grid">
                <!-- Chart Section -->
                <div class="chart-section">
                    <div class="chart-header">
                        <h3>Market Overview</h3>
                        <div class="chart-tabs">
                            <button class="chart-tab active" data-period="1D">1D</button>
                            <button class="chart-tab" data-period="1W">1W</button>
                            <button class="chart-tab" data-period="1M">1M</button>
                            <button class="chart-tab" data-period="3M">3M</button>
                            <button class="chart-tab" data-period="1Y">1Y</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div style="text-align: center;">
                            <i class="fas fa-chart-area" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                            <h4>Advanced Trading Chart</h4>
                            <p>Real-time market data with AI-powered technical indicators</p>
                            <button class="btn btn-primary" style="margin-top: 1rem;">
                                <i class="fas fa-external-link-alt"></i>
                                Open Full Chart
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Insights Panel -->
                <div class="insights-panel">
                    <!-- Market Sentiment -->
                    <div class="market-sentiment">
                        <h4 style="margin-bottom: 1rem;">Market Sentiment</h4>
                        <div class="sentiment-score">Bullish</div>
                        <div class="sentiment-gauge">
                            <div class="sentiment-fill"></div>
                        </div>
                        <p style="margin: 0; opacity: 0.9; font-size: 0.875rem;">
                            AI analysis indicates strong bullish sentiment across major cryptocurrencies
                        </p>
                    </div>

                    <!-- AI Insights -->
                    <div class="insight-card">
                        <div class="insight-header">
                            <div class="insight-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h4>AI Market Insights</h4>
                        </div>
                        <div class="insight-content">
                            <p><strong>Key Trend:</strong> Bitcoin showing strong support at $65,000 level with increasing institutional adoption.</p>
                            <p><strong>Opportunity:</strong> DeFi tokens experiencing renewed interest with 15% average gain this week.</p>
                            <p><strong>Risk Alert:</strong> High correlation between major altcoins suggests potential for synchronized movements.</p>
                        </div>
                    </div>

                    <!-- News Impact -->
                    <div class="insight-card">
                        <div class="insight-header">
                            <div class="insight-icon">
                                <i class="fas fa-newspaper"></i>
                            </div>
                            <h4>News Impact Analysis</h4>
                        </div>
                        <div class="insight-content">
                            <div style="margin-bottom: 1rem;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                    <span style="font-weight: 600;">ETF Approval News</span>
                                    <span style="color: var(--success); font-weight: 600;">+8.2%</span>
                                </div>
                                <div style="font-size: 0.875rem; color: var(--gray-600);">Positive impact on BTC and ETH prices</div>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                    <span style="font-weight: 600;">Fed Rate Decision</span>
                                    <span style="color: var(--warning); font-weight: 600;">-2.1%</span>
                                </div>
                                <div style="font-size: 0.875rem; color: var(--gray-600);">Temporary market uncertainty</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Movers -->
            <div class="top-movers">
                <div class="chart-header">
                    <h3>Top Movers (24h)</h3>
                    <button class="btn btn-outline btn-sm">View All</button>
                </div>
                
                <div class="mover-item">
                    <div class="mover-info">
                        <div class="mover-icon">KC</div>
                        <div class="mover-details">
                            <h5>Kontour Coin</h5>
                            <p>KONT</p>
                        </div>
                    </div>
                    <div class="mover-change change-positive">
                        <div>+15.7%</div>
                        <div style="font-size: 0.875rem; opacity: 0.8;">$2.4567</div>
                    </div>
                </div>
                
                <div class="mover-item">
                    <div class="mover-info">
                        <div class="mover-icon" style="background: #f7931a;">₿</div>
                        <div class="mover-details">
                            <h5>Bitcoin</h5>
                            <p>BTC</p>
                        </div>
                    </div>
                    <div class="mover-change change-positive">
                        <div>+8.3%</div>
                        <div style="font-size: 0.875rem; opacity: 0.8;">$67,234</div>
                    </div>
                </div>
                
                <div class="mover-item">
                    <div class="mover-info">
                        <div class="mover-icon" style="background: #627eea;">Ξ</div>
                        <div class="mover-details">
                            <h5>Ethereum</h5>
                            <p>ETH</p>
                        </div>
                    </div>
                    <div class="mover-change change-positive">
                        <div>+6.1%</div>
                        <div style="font-size: 0.875rem; opacity: 0.8;">$3,456</div>
                    </div>
                </div>
                
                <div class="mover-item">
                    <div class="mover-info">
                        <div class="mover-icon" style="background: #00d4aa;">P</div>
                        <div class="mover-details">
                            <h5>Polygon</h5>
                            <p>MATIC</p>
                        </div>
                    </div>
                    <div class="mover-change change-negative">
                        <div>-3.2%</div>
                        <div style="font-size: 0.875rem; opacity: 0.8;">$0.89</div>
                    </div>
                </div>
                
                <div class="mover-item">
                    <div class="mover-info">
                        <div class="mover-icon" style="background: #ff6b35;">S</div>
                        <div class="mover-details">
                            <h5>Solana</h5>
                            <p>SOL</p>
                        </div>
                    </div>
                    <div class="mover-change change-positive">
                        <div>+12.8%</div>
                        <div style="font-size: 0.875rem; opacity: 0.8;">$156.78</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="scripts/advanced-main.js"></script>
    <script>
        // Initialize analytics dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            initializeAnalyticsDashboard();
        });

        function initializeAnalyticsDashboard() {
            // Initialize chart tabs
            const chartTabs = document.querySelectorAll('.chart-tab');
            chartTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    chartTabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    updateChart(tab.dataset.period);
                });
            });

            // Update metrics
            updateAnalyticsMetrics();
            
            // Start real-time updates
            setInterval(updateAnalyticsMetrics, 15000);
        }

        function updateChart(period) {
            console.log(`Updating chart for period: ${period}`);
            // Chart update logic would go here
        }

        function updateAnalyticsMetrics() {
            // Simulate real-time metric updates
            const metrics = {
                totalMarketCap: 2.4 + (Math.random() - 0.5) * 0.2,
                volume24h: 156.7 + (Math.random() - 0.5) * 20,
                btcDominance: 42.3 + (Math.random() - 0.5) * 2,
                fearGreedIndex: 67 + (Math.random() - 0.5) * 10
            };

            document.getElementById('totalMarketCap').textContent = `$${metrics.totalMarketCap.toFixed(1)}T`;
            document.getElementById('volume24h').textContent = `$${metrics.volume24h.toFixed(1)}B`;
            document.getElementById('btcDominance').textContent = `${metrics.btcDominance.toFixed(1)}%`;
            document.getElementById('fearGreedIndex').textContent = Math.round(metrics.fearGreedIndex);
        }
    </script>
</body>
</html>
