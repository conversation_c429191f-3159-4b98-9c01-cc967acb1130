/* Professional Website Enhancements for Kontour Coin */

/* Advanced Color Palette */
:root {
    --primary-gradient: linear-gradient(135deg, #6B46C1 0%, #9333EA 50%, #7C3AED 100%);
    --secondary-gradient: linear-gradient(135deg, #1E293B 0%, #334155 50%, #475569 100%);
    --accent-gradient: linear-gradient(135deg, #F59E0B 0%, #EAB308 50%, #FACC15 100%);
    --success-gradient: linear-gradient(135deg, #059669 0%, #10B981 50%, #34D399 100%);
    --danger-gradient: linear-gradient(135deg, #DC2626 0%, #EF4444 50%, #F87171 100%);
    
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    --text-primary: #F8FAFC;
    --text-secondary: #CBD5E1;
    --text-muted: #94A3B8;
    
    --surface-primary: rgba(30, 41, 59, 0.95);
    --surface-secondary: rgba(51, 65, 85, 0.9);
    --surface-tertiary: rgba(71, 85, 105, 0.8);
}

/* Professional Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: -0.01em;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.mono {
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

/* Glass Morphism Effects */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--glass-shadow);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Professional Navigation */
.professional-nav {
    background: var(--surface-primary);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.nav-logo .logo-icon {
    font-size: 2rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
    background: var(--glass-bg);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

/* Professional Buttons */
.btn-professional {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-professional::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-professional:hover::before {
    left: 100%;
}

.btn-primary-pro {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 20px rgba(107, 70, 193, 0.3);
}

.btn-primary-pro:hover {
    box-shadow: 0 8px 30px rgba(107, 70, 193, 0.4);
    transform: translateY(-2px);
}

.btn-secondary-pro {
    background: var(--surface-secondary);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
}

.btn-secondary-pro:hover {
    background: var(--surface-tertiary);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Professional Cards */
.professional-card {
    background: var(--surface-primary);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.professional-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.professional-card:hover::before {
    opacity: 1;
}

.professional-card:hover {
    background: var(--surface-secondary);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

/* Professional Metrics */
.metric-card-pro {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.metric-value-pro {
    font-size: 2.5rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.metric-label-pro {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.metric-change-pro {
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-block;
}

.metric-change-pro.positive {
    background: rgba(16, 185, 129, 0.2);
    color: #34D399;
}

.metric-change-pro.negative {
    background: rgba(239, 68, 68, 0.2);
    color: #F87171;
}

/* Professional Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--surface-secondary) 25%, var(--surface-tertiary) 50%, var(--surface-secondary) 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
    border-radius: 8px;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Professional Animations */
.fade-in-up {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stagger-animation > * {
    animation-delay: calc(var(--stagger-delay, 0) * 0.1s);
}

/* Professional Status Indicators */
.status-indicator-pro {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator-pro::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-active::before {
    background: #10B981;
}

.status-warning::before {
    background: #F59E0B;
}

.status-error::before {
    background: #EF4444;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Professional Progress Bars */
.progress-bar-pro {
    width: 100%;
    height: 8px;
    background: var(--surface-tertiary);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill-pro {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-fill-pro::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Professional Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .professional-card {
        padding: 1.5rem;
    }
    
    .metric-value-pro {
        font-size: 2rem;
    }
    
    .btn-professional {
        padding: 0.625rem 1.25rem;
        font-size: 0.8rem;
    }
}

/* Professional Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(0, 0, 0, 0.2);
        --glass-border: rgba(255, 255, 255, 0.1);
        --surface-primary: rgba(15, 23, 42, 0.95);
        --surface-secondary: rgba(30, 41, 59, 0.9);
        --surface-tertiary: rgba(51, 65, 85, 0.8);
    }
}
