/**
 * Kontour Coin - Advanced Professional Website JavaScript
 * Enhanced functionality with animations, real-time data, and interactive features
 */

// Global application state
const KontourApp = {
    initialized: false,
    animations: {
        observers: [],
        counters: []
    },
    data: {
        price: 0,
        priceChange: 0,
        marketCap: 0,
        volume: 0
    },
    config: {
        apiEndpoint: 'https://api.kontourcoin.com',
        wsEndpoint: 'wss://ws.kontourcoin.com',
        updateInterval: 30000 // 30 seconds
    }
};

/**
 * Initialize the application
 */
function initializeApp() {
    if (KontourApp.initialized) return;
    
    console.log('🚀 Initializing Kontour Coin Website...');
    
    // Initialize core features
    initializeNavigation();
    initializeAnimations();
    initializeCounters();
    initializeLiveData();
    initializeScrollEffects();
    initializeMobileMenu();
    initializeFormHandlers();
    
    KontourApp.initialized = true;
    console.log('✅ Kontour Coin Website Initialized');
}

/**
 * Navigation functionality
 */
function initializeNavigation() {
    const navbar = document.getElementById('navbar');
    const navLinks = document.querySelectorAll('.navbar-nav a, .mobile-menu-nav a');
    
    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const href = link.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                    
                    // Update active link
                    updateActiveNavLink(href);
                    
                    // Close mobile menu if open
                    closeMobileMenu();
                }
            }
        });
    });
    
    // Update active navigation link based on scroll position
    window.addEventListener('scroll', updateActiveNavOnScroll);
}

/**
 * Update active navigation link
 */
function updateActiveNavLink(activeHref) {
    document.querySelectorAll('.navbar-nav a, .mobile-menu-nav a').forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === activeHref) {
            link.classList.add('active');
        }
    });
}

/**
 * Update active navigation based on scroll position
 */
function updateActiveNavOnScroll() {
    const sections = document.querySelectorAll('section[id]');
    const scrollPos = window.scrollY + 100;
    
    sections.forEach(section => {
        const top = section.offsetTop;
        const bottom = top + section.offsetHeight;
        const id = section.getAttribute('id');
        
        if (scrollPos >= top && scrollPos <= bottom) {
            updateActiveNavLink(`#${id}`);
        }
    });
}

/**
 * Initialize scroll-triggered animations
 */
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.slide-up, .feature-card, .testimonial-card');
    animatedElements.forEach(el => {
        observer.observe(el);
    });
    
    KontourApp.animations.observers.push(observer);
}

/**
 * Initialize animated counters
 */
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
    
    KontourApp.animations.observers.push(counterObserver);
}

/**
 * Animate counter numbers
 */
function animateCounter(element) {
    const target = element.textContent;
    const isPrice = element.id === 'livePrice';
    const isPercentage = target.includes('%');
    const isCurrency = target.includes('$') || target.includes('B') || target.includes('M');
    
    let endValue = parseFloat(target.replace(/[^0-9.-]/g, ''));
    let startValue = 0;
    let suffix = '';
    let prefix = '';
    
    // Extract prefix and suffix
    if (target.includes('$')) prefix = '$';
    if (target.includes('%')) suffix = '%';
    if (target.includes('B')) suffix = 'B';
    if (target.includes('M')) suffix = 'M';
    if (target.includes('+')) suffix = '+' + suffix;
    
    const duration = 2000; // 2 seconds
    const increment = endValue / (duration / 16); // 60fps
    
    const timer = setInterval(() => {
        startValue += increment;
        if (startValue >= endValue) {
            startValue = endValue;
            clearInterval(timer);
        }
        
        let displayValue = startValue;
        if (isPrice) {
            displayValue = startValue.toFixed(4);
        } else if (isPercentage) {
            displayValue = startValue.toFixed(1);
        } else if (isCurrency && endValue >= 1000) {
            displayValue = startValue.toFixed(1);
        } else {
            displayValue = Math.floor(startValue);
        }
        
        element.textContent = prefix + displayValue + suffix;
    }, 16);
    
    KontourApp.animations.counters.push(timer);
}

/**
 * Initialize live data updates
 */
function initializeLiveData() {
    // Simulate live price data
    updateLiveData();
    
    // Update data periodically
    setInterval(updateLiveData, KontourApp.config.updateInterval);
}

/**
 * Update live market data
 */
function updateLiveData() {
    // Simulate realistic price movements
    const basePrice = 2.4567;
    const volatility = 0.05; // 5% volatility
    const change = (Math.random() - 0.5) * volatility;
    
    KontourApp.data.price = basePrice * (1 + change);
    KontourApp.data.priceChange = change * 100;
    KontourApp.data.marketCap = KontourApp.data.price * 1000000000; // 1B tokens
    KontourApp.data.volume = Math.random() * 50000000 + 10000000; // 10-60M volume
    
    // Update DOM elements
    updatePriceDisplay();
}

/**
 * Update price display elements
 */
function updatePriceDisplay() {
    const priceElement = document.getElementById('livePrice');
    const changeElement = document.getElementById('priceChange');
    const marketCapElement = document.getElementById('marketCap');
    
    if (priceElement) {
        priceElement.textContent = `$${KontourApp.data.price.toFixed(4)}`;
        priceElement.style.color = KontourApp.data.priceChange >= 0 ? 'var(--success)' : 'var(--error)';
    }
    
    if (changeElement) {
        const sign = KontourApp.data.priceChange >= 0 ? '+' : '';
        changeElement.textContent = `${sign}${KontourApp.data.priceChange.toFixed(2)}%`;
        changeElement.style.color = KontourApp.data.priceChange >= 0 ? 'var(--success)' : 'var(--error)';
    }
    
    if (marketCapElement) {
        const marketCapB = (KontourApp.data.marketCap / 1000000000).toFixed(1);
        marketCapElement.textContent = `$${marketCapB}B`;
    }
}

/**
 * Initialize scroll effects
 */
function initializeScrollEffects() {
    // Parallax effect for hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero');
        
        if (hero) {
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        }
    });
}

/**
 * Initialize mobile menu
 */
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileMenuClose = document.getElementById('mobileMenuClose');
    
    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', openMobileMenu);
    }
    
    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', closeMobileMenu);
    }
    
    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
        if (mobileMenu && mobileMenu.classList.contains('active')) {
            if (!mobileMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                closeMobileMenu();
            }
        }
    });
}

/**
 * Open mobile menu
 */
function openMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenu) {
        mobileMenu.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close mobile menu
 */
function closeMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenu) {
        mobileMenu.classList.remove('active');
        document.body.style.overflow = '';
    }
}

/**
 * Initialize form handlers
 */
function initializeFormHandlers() {
    // Newsletter signup
    const newsletterForms = document.querySelectorAll('.newsletter-form');
    newsletterForms.forEach(form => {
        form.addEventListener('submit', handleNewsletterSignup);
    });
    
    // Contact forms
    const contactForms = document.querySelectorAll('.contact-form');
    contactForms.forEach(form => {
        form.addEventListener('submit', handleContactForm);
    });
}

/**
 * Handle newsletter signup
 */
function handleNewsletterSignup(e) {
    e.preventDefault();
    const form = e.target;
    const email = form.querySelector('input[type="email"]').value;
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<span class="loading"></span> Subscribing...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        submitBtn.textContent = '✓ Subscribed!';
        submitBtn.style.background = 'var(--success)';
        
        setTimeout(() => {
            submitBtn.textContent = originalText;
            submitBtn.style.background = '';
            submitBtn.disabled = false;
            form.reset();
        }, 2000);
    }, 1500);
}

/**
 * Handle contact form submission
 */
function handleContactForm(e) {
    e.preventDefault();
    const form = e.target;
    
    // Show success message
    showNotification('Message sent successfully! We\'ll get back to you soon.', 'success');
    form.reset();
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--${type === 'success' ? 'success' : 'info'});
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: var(--z-toast);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 5000);
}

/**
 * Utility functions
 */
const Utils = {
    // Debounce function
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Throttle function
    throttle: (func, limit) => {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // Format number with commas
    formatNumber: (num) => {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    
    // Format currency
    formatCurrency: (amount, currency = 'USD') => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }
};

// Export for use in other scripts
window.KontourApp = KontourApp;
window.Utils = Utils;
