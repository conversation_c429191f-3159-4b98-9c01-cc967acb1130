{"name": "kontour-web3-integration-service", "version": "1.0.0", "description": "Professional Web3 Integration Service with 99.87% Accuracy for Multi-Chain Support", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["web3", "blockchain", "ethereum", "polygon", "bsc", "avalanche", "arbitrum", "optimism", "defi", "smart-contracts", "wallet-integration", "kontour"], "author": "Kontour Coin Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "web3": "^4.3.0", "ethers": "^6.8.1", "node-fetch": "^3.3.2", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}