<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>Quantum Wallet | Kontour Coin 💎</title>
    <meta name="description" content="Secure quantum-encrypted wallet with multi-chain support, DeFi integration, and AI-powered portfolio management.">
    <meta name="keywords" content="cryptocurrency wallet, quantum security, multi-chain wallet, DeFi wallet, secure crypto storage">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles/advanced-professional.css" as="style">
    <link rel="preload" href="scripts/advanced-main.js" as="script">
    
    <!-- External resources -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link href="styles/advanced-professional.css" rel="stylesheet">
    
    <!-- Favicons -->
    <link rel="icon" type="image/svg+xml" href="assets/kontour-logo.svg">
    
    <style>
        .wallet-dashboard {
            padding-top: 80px;
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        .wallet-header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: var(--space-8) 0;
            margin-bottom: var(--space-8);
        }
        
        .wallet-balance {
            text-align: center;
            margin-bottom: var(--space-6);
        }
        
        .balance-amount {
            font-size: var(--text-5xl);
            font-weight: var(--font-extrabold);
            margin-bottom: var(--space-2);
        }
        
        .balance-usd {
            font-size: var(--text-xl);
            opacity: 0.9;
        }
        
        .wallet-actions {
            display: flex;
            justify-content: center;
            gap: var(--space-4);
            flex-wrap: wrap;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-lg);
            text-decoration: none;
            font-weight: var(--font-medium);
            transition: var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            text-decoration: none;
            color: var(--white);
        }
        
        .wallet-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--space-8);
            margin-bottom: var(--space-8);
        }
        
        .assets-section {
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }
        
        .section-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .asset-list {
            padding: 0;
        }
        
        .asset-item {
            display: flex;
            align-items: center;
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--gray-100);
            transition: var(--transition-fast);
        }
        
        .asset-item:hover {
            background: var(--gray-50);
        }
        
        .asset-item:last-child {
            border-bottom: none;
        }
        
        .asset-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-full);
            background: var(--gradient-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--space-4);
            color: var(--white);
            font-weight: var(--font-bold);
            font-size: var(--text-lg);
        }
        
        .asset-info {
            flex: 1;
        }
        
        .asset-name {
            font-weight: var(--font-semibold);
            color: var(--gray-900);
            margin-bottom: var(--space-1);
        }
        
        .asset-symbol {
            color: var(--gray-500);
            font-size: var(--text-sm);
        }
        
        .asset-balance {
            text-align: right;
        }
        
        .balance-amount-asset {
            font-weight: var(--font-semibold);
            color: var(--gray-900);
            margin-bottom: var(--space-1);
        }
        
        .balance-usd-asset {
            color: var(--gray-500);
            font-size: var(--text-sm);
        }
        
        .sidebar-section {
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            border: 1px solid var(--gray-200);
        }
        
        .security-status {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }
        
        .security-icon {
            width: 40px;
            height: 40px;
            background: var(--success);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
        }
        
        .security-info h4 {
            margin: 0 0 var(--space-1) 0;
            color: var(--gray-900);
        }
        
        .security-info p {
            margin: 0;
            color: var(--gray-600);
            font-size: var(--text-sm);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            margin-bottom: var(--space-3);
            color: var(--gray-700);
        }
        
        .feature-list li:last-child {
            margin-bottom: 0;
        }
        
        .feature-list i {
            color: var(--success);
            margin-right: var(--space-3);
            width: 16px;
        }
        
        .transaction-item {
            display: flex;
            align-items: center;
            padding: var(--space-4) 0;
            border-bottom: 1px solid var(--gray-100);
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
        
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--space-3);
            color: var(--white);
        }
        
        .transaction-icon.send {
            background: var(--error);
        }
        
        .transaction-icon.receive {
            background: var(--success);
        }
        
        .transaction-icon.swap {
            background: var(--info);
        }
        
        .transaction-details {
            flex: 1;
        }
        
        .transaction-type {
            font-weight: var(--font-medium);
            color: var(--gray-900);
            margin-bottom: var(--space-1);
        }
        
        .transaction-time {
            color: var(--gray-500);
            font-size: var(--text-sm);
        }
        
        .transaction-amount {
            text-align: right;
            font-weight: var(--font-semibold);
        }
        
        .amount-positive {
            color: var(--success);
        }
        
        .amount-negative {
            color: var(--error);
        }
        
        @media (max-width: 768px) {
            .wallet-grid {
                grid-template-columns: 1fr;
            }
            
            .wallet-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .action-btn {
                width: 200px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="navbar-container">
                <a href="index-new.html" class="navbar-brand">
                    <img src="assets/kontour-logo.svg" alt="Kontour Coin" class="navbar-logo">
                    <span>Kontour Coin</span>
                </a>
                
                <ul class="navbar-nav">
                    <li><a href="index-new.html">Home</a></li>
                    <li><a href="trading.html">Trading</a></li>
                    <li><a href="wallet.html" class="active">Wallet</a></li>
                    <li><a href="analytics.html">Analytics</a></li>
                    <li><a href="ai-agents.html">AI Agents</a></li>
                </ul>
                
                <div class="navbar-actions">
                    <a href="#settings" class="btn btn-outline btn-sm">Settings</a>
                    <a href="#profile" class="btn btn-primary btn-sm">Profile</a>
                </div>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Wallet Dashboard -->
    <div class="wallet-dashboard">
        <!-- Wallet Header -->
        <div class="wallet-header">
            <div class="container">
                <div class="wallet-balance">
                    <div class="balance-amount" id="totalBalance">$125,432.67</div>
                    <div class="balance-usd">Total Portfolio Value</div>
                </div>
                
                <div class="wallet-actions">
                    <a href="#send" class="action-btn">
                        <i class="fas fa-paper-plane"></i>
                        Send
                    </a>
                    <a href="#receive" class="action-btn">
                        <i class="fas fa-qrcode"></i>
                        Receive
                    </a>
                    <a href="#swap" class="action-btn">
                        <i class="fas fa-exchange-alt"></i>
                        Swap
                    </a>
                    <a href="#stake" class="action-btn">
                        <i class="fas fa-coins"></i>
                        Stake
                    </a>
                    <a href="#defi" class="action-btn">
                        <i class="fas fa-chart-pie"></i>
                        DeFi
                    </a>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="wallet-grid">
                <!-- Assets Section -->
                <div class="assets-section">
                    <div class="section-header">
                        <h3>Your Assets</h3>
                        <button class="btn btn-outline btn-sm">
                            <i class="fas fa-plus"></i>
                            Add Token
                        </button>
                    </div>
                    
                    <div class="asset-list">
                        <div class="asset-item">
                            <div class="asset-icon">KC</div>
                            <div class="asset-info">
                                <div class="asset-name">Kontour Coin</div>
                                <div class="asset-symbol">KONT</div>
                            </div>
                            <div class="asset-balance">
                                <div class="balance-amount-asset">45,234.56 KONT</div>
                                <div class="balance-usd-asset">$111,234.67</div>
                            </div>
                        </div>
                        
                        <div class="asset-item">
                            <div class="asset-icon" style="background: #f7931a;">₿</div>
                            <div class="asset-info">
                                <div class="asset-name">Bitcoin</div>
                                <div class="asset-symbol">BTC</div>
                            </div>
                            <div class="asset-balance">
                                <div class="balance-amount-asset">0.2345 BTC</div>
                                <div class="balance-usd-asset">$10,234.56</div>
                            </div>
                        </div>
                        
                        <div class="asset-item">
                            <div class="asset-icon" style="background: #627eea;">Ξ</div>
                            <div class="asset-info">
                                <div class="asset-name">Ethereum</div>
                                <div class="asset-symbol">ETH</div>
                            </div>
                            <div class="asset-balance">
                                <div class="balance-amount-asset">1.5678 ETH</div>
                                <div class="balance-usd-asset">$3,456.78</div>
                            </div>
                        </div>
                        
                        <div class="asset-item">
                            <div class="asset-icon" style="background: #26a17b;">$</div>
                            <div class="asset-info">
                                <div class="asset-name">USD Coin</div>
                                <div class="asset-symbol">USDC</div>
                            </div>
                            <div class="asset-balance">
                                <div class="balance-amount-asset">506.66 USDC</div>
                                <div class="balance-usd-asset">$506.66</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div>
                    <!-- Security Status -->
                    <div class="sidebar-section">
                        <h4 style="margin-bottom: 1.5rem;">Quantum Security</h4>
                        
                        <div class="security-status">
                            <div class="security-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="security-info">
                                <h4>Fully Protected</h4>
                                <p>Quantum encryption active</p>
                            </div>
                        </div>
                        
                        <ul class="feature-list">
                            <li>
                                <i class="fas fa-check"></i>
                                Multi-signature protection
                            </li>
                            <li>
                                <i class="fas fa-check"></i>
                                Hardware wallet support
                            </li>
                            <li>
                                <i class="fas fa-check"></i>
                                Biometric authentication
                            </li>
                            <li>
                                <i class="fas fa-check"></i>
                                Cold storage backup
                            </li>
                        </ul>
                    </div>

                    <!-- AI Portfolio Manager -->
                    <div class="sidebar-section">
                        <h4 style="margin-bottom: 1.5rem;">AI Portfolio Manager</h4>
                        
                        <div style="background: var(--gradient-primary); color: var(--white); padding: 1.5rem; border-radius: var(--radius-lg); margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                                <i class="fas fa-robot" style="margin-right: 0.5rem;"></i>
                                <span style="font-weight: 600;">Portfolio Score: 8.7/10</span>
                            </div>
                            <p style="margin: 0; font-size: 0.875rem; opacity: 0.9;">
                                Your portfolio is well-diversified with optimal risk distribution.
                            </p>
                        </div>
                        
                        <button class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-magic"></i>
                            Optimize Portfolio
                        </button>
                    </div>

                    <!-- Recent Transactions -->
                    <div class="sidebar-section">
                        <h4 style="margin-bottom: 1.5rem;">Recent Transactions</h4>
                        
                        <div class="transaction-item">
                            <div class="transaction-icon receive">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Received KONT</div>
                                <div class="transaction-time">2 hours ago</div>
                            </div>
                            <div class="transaction-amount amount-positive">
                                +1,000 KONT
                            </div>
                        </div>
                        
                        <div class="transaction-item">
                            <div class="transaction-icon swap">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Swapped ETH → KONT</div>
                                <div class="transaction-time">1 day ago</div>
                            </div>
                            <div class="transaction-amount">
                                0.5 ETH
                            </div>
                        </div>
                        
                        <div class="transaction-item">
                            <div class="transaction-icon send">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Sent BTC</div>
                                <div class="transaction-time">3 days ago</div>
                            </div>
                            <div class="transaction-amount amount-negative">
                                -0.1 BTC
                            </div>
                        </div>
                        
                        <button class="btn btn-outline" style="width: 100%; margin-top: 1rem;">
                            View All Transactions
                        </button>
                    </div>
                </div>
            </div>

            <!-- DeFi Opportunities -->
            <div class="card" style="margin-bottom: 2rem;">
                <div class="card-header">
                    <h3>DeFi Opportunities</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-3" style="gap: 2rem;">
                        <div style="text-align: center; padding: 1.5rem; background: var(--gray-50); border-radius: var(--radius-lg);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🌾</div>
                            <h4 style="margin-bottom: 0.5rem;">Yield Farming</h4>
                            <p style="color: var(--gray-600); margin-bottom: 1rem;">Earn up to 15.7% APY</p>
                            <button class="btn btn-primary btn-sm">Start Farming</button>
                        </div>
                        
                        <div style="text-align: center; padding: 1.5rem; background: var(--gray-50); border-radius: var(--radius-lg);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🏦</div>
                            <h4 style="margin-bottom: 0.5rem;">Lending</h4>
                            <p style="color: var(--gray-600); margin-bottom: 1rem;">Earn up to 8.3% APY</p>
                            <button class="btn btn-primary btn-sm">Start Lending</button>
                        </div>
                        
                        <div style="text-align: center; padding: 1.5rem; background: var(--gray-50); border-radius: var(--radius-lg);">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">💧</div>
                            <h4 style="margin-bottom: 0.5rem;">Liquidity Pool</h4>
                            <p style="color: var(--gray-600); margin-bottom: 1rem;">Earn up to 12.4% APY</p>
                            <button class="btn btn-primary btn-sm">Add Liquidity</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="scripts/advanced-main.js"></script>
    <script>
        // Initialize wallet dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            initializeWalletDashboard();
        });

        function initializeWalletDashboard() {
            // Update portfolio values
            updatePortfolioValues();
            
            // Start real-time updates
            setInterval(updateWalletData, 10000);
        }

        function updatePortfolioValues() {
            // Simulate portfolio updates
            const totalValue = 125432.67 + (Math.random() - 0.5) * 1000;
            document.getElementById('totalBalance').textContent = `$${totalValue.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
        }

        function updateWalletData() {
            // Simulate real-time wallet updates
            updatePortfolioValues();
        }
    </script>
</body>
</html>
