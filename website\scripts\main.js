document.addEventListener('DOMContentLoaded', function() { initializeWebsite(); }); function initializeWebsite() { setupNavigation(); setupDashboardTabs(); setupServiceMonitoring(); setupAnimations(); updateRealTimeData(); setInterval(updateRealTimeData, 5000); setInterval(updateServiceStatus, 10000); } function setupNavigation() { const navToggle = document.querySelector('.nav-toggle'); const navMenu = document.querySelector('.nav-menu'); if (navToggle) { navToggle.addEventListener('click', () => { navMenu.classList.toggle('active'); }); } document.querySelectorAll('a[href^="#"]').forEach(anchor => { anchor.addEventListener('click', function (e) { e.preventDefault(); const target = document.querySelector(this.getAttribute('href')); if (target) { target.scrollIntoView({ behavior: 'smooth', block: 'start' }); } }); }); } function setupDashboardTabs() { const tabButtons = document.querySelectorAll('.tab-btn'); const tabContents = document.querySelectorAll('.tab-content'); tabButtons.forEach(button => { button.addEventListener('click', () => { const targetTab = button.getAttribute('data-tab'); tabButtons.forEach(btn => btn.classList.remove('active')); tabContents.forEach(content => content.classList.remove('active')); button.classList.add('active'); const targetContent = document.getElementById(targetTab); if (targetContent) { targetContent.classList.add('active'); } loadTabContent(targetTab); }); }); } function loadTabContent(tabName) { switch(tabName) { case 'agents': loadAgentsData(); break; case 'workflows': loadWorkflowsData(); break; case 'analytics': loadAnalyticsData(); break; default: break; } } function updateRealTimeData() { updateElement('active-agents', Math.floor(Math.random() * 5) + 10); updateElement('completed-tasks', Math.floor(Math.random() * 100) + 1200); updateElement('system-uptime', (99.5 + Math.random() * 0.4).toFixed(1) + '%'); updateElement('dashboard-agents', Math.floor(Math.random() * 5) + 10); updateElement('dashboard-tasks', Math.floor(Math.random() * 100) + 800); updateElement('dashboard-performance', (90 + Math.random() * 8).toFixed(1) + '%'); updateElement('dashboard-profit', '+' + (20 + Math.random() * 10).toFixed(1) + '%'); } function updateElement(id, value) { const element = document.getElementById(id); if (element) { element.textContent = value; } } function setupServiceMonitoring() { const services = [ { name: 'API Gateway', port: 8080, icon: '🌐' }, { name: 'Wallet Service', port: 3001, icon: '💰' }, { name: 'Real-time Service', port: 8035, icon: '⚡' }, { name: 'Agentic AI Service', port: 8070, icon: '🤖' }, { name: 'Neural Network Service', port: 8050, icon: '🧠' }, { name: 'Big Data Service', port: 8040, icon: '📊' }, { name: 'IoT Processing Service', port: 8060, icon: '🌐' }, { name: 'AI Service', port: 8020, icon: '🧠' }, { name: 'Integration Service', port: 8010, icon: '🔗' } ]; const servicesGrid = document.getElementById('services-grid'); if (servicesGrid) { servicesGrid.innerHTML = services.map(service => createServiceCard(service)).join(''); } updateServiceStatus(); } function createServiceCard(service) { return ` <div class="service-card" id="service-${service.port}"> <div class="service-header"> <div class="service-title"> <span class="service-icon">${service.icon}</span> ${service.name} </div> <span class="status-badge status-starting" id="status-${service.port}">Checking...</span> </div> <div class="service-details"> <div class="service-url">http: <div class="service-actions"> <button class="btn btn-primary btn-sm" onclick="testService(${service.port})"> Test Health </button> <button class="btn btn-secondary btn-sm" onclick="openService(${service.port})"> Open </button> </div> </div> </div> `; } async function updateServiceStatus() { const services = [8080, 3001, 8035, 8070, 8050, 8040, 8060, 8020, 8010]; for (const port of services) { try { const response = await fetch(`http: method: 'GET', mode: 'cors', timeout: 5000 }); const statusElement = document.getElementById(`status-${port}`); if (statusElement) { if (response.ok) { statusElement.textContent = 'Running'; statusElement.className = 'status-badge status-running'; } else { statusElement.textContent = 'Error'; statusElement.className = 'status-badge status-error'; } } } catch (error) { const statusElement = document.getElementById(`status-${port}`); if (statusElement) { statusElement.textContent = 'Offline'; statusElement.className = 'status-badge status-error'; } } } } function testService(port) { fetch(`http: .then(response => { if (response.ok) { showNotification(`Service on port ${port} is healthy!`, 'success'); } else { showNotification(`Service on port ${port} returned error!`, 'error'); } }) .catch(error => { showNotification(`Service on port ${port} is not reachable!`, 'error'); }); } function openService(port) { window.open(`http: } async function loadAgentsData() { try { const response = await fetch('http: const data = await response.json(); const agentsList = document.getElementById('agents-list'); if (agentsList && data.agents) { agentsList.innerHTML = data.agents.map(agent => createAgentCard(agent)).join(''); } } catch (error) { console.error('Error loading agents data:', error); showMockAgents(); } } function createAgentCard(agent) { return ` <div class="agent-card"> <div class="agent-header"> <h4>${agent.name}</h4> <span class="status-badge status-${agent.status}">${agent.status}</span> </div> <div class="agent-details"> <p>Type: ${agent.type}</p> <p>Performance: ${(agent.performance * 100).toFixed(1)}%</p> <p>Tasks: ${agent.tasksCompleted}</p> </div> </div> `; } function showMockAgents() { const mockAgents = [ { name: 'Coordinator Agent Alpha', type: 'coordinator', status: 'active', performance: 0.94, tasksCompleted: 156 }, { name: 'Specialist Agent Beta', type: 'specialist', status: 'active', performance: 0.87, tasksCompleted: 203 }, { name: 'Validator Agent Gamma', type: 'validator', status: 'idle', performance: 0.91, tasksCompleted: 89 }, { name: 'Optimizer Agent Delta', type: 'optimizer', status: 'training', performance: 0.88, tasksCompleted: 134 }, { name: 'Learner Agent Epsilon', type: 'learner', status: 'active', performance: 0.92, tasksCompleted: 167 } ]; const agentsList = document.getElementById('agents-list'); if (agentsList) { agentsList.innerHTML = mockAgents.map(agent => createAgentCard(agent)).join(''); } } function setupAnimations() { const observerOptions = { threshold: 0.1, rootMargin: '0px 0px -50px 0px' }; const observer = new IntersectionObserver((entries) => { entries.forEach(entry => { if (entry.isIntersecting) { entry.target.classList.add('animate-in'); } }); }, observerOptions); document.querySelectorAll('.feature-card, .metric-card, .service-card').forEach(el => { observer.observe(el); }); } function openWorkflowBuilder() { showNotification('Workflow Builder opening...', 'info'); setTimeout(() => { alert('Workflow Builder would open here with drag-and-drop interface for creating AI agent workflows!'); }, 500); } function showNotification(message, type = 'info') { const notification = document.createElement('div'); notification.className = `notification notification-${type}`; notification.textContent = message; Object.assign(notification.style, { position: 'fixed', top: '20px', right: '20px', padding: '12px 24px', borderRadius: '8px', color: 'white', fontWeight: '500', zIndex: '10000', transform: 'translateX(100%)', transition: 'transform 0.3s ease' }); const colors = { success: '#10B981', error: '#EF4444', warning: '#F59E0B', info: '#8B5CF6' }; notification.style.backgroundColor = colors[type] || colors.info; document.body.appendChild(notification); setTimeout(() => { notification.style.transform = 'translateX(0)'; }, 100); setTimeout(() => { notification.style.transform = 'translateX(100%)'; setTimeout(() => { document.body.removeChild(notification); }, 300); }, 3000); } function initializeCharts() { const ctx = document.getElementById('performanceChart'); if (ctx && typeof Chart !== 'undefined') { new Chart(ctx, { type: 'line', data: { labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'], datasets: [{ label: 'Performance', data: [85, 87, 91, 89, 94, 92], borderColor: '#8B5CF6', backgroundColor: 'rgba(139, 92, 246, 0.1)', tension: 0.4 }] }, options: { responsive: true, plugins: { legend: { display: false } }, scales: { y: { beginAtZero: true, max: 100 } } } }); } } if (typeof Chart !== 'undefined') { initializeCharts(); } else { window.addEventListener('load', initializeCharts); }

// AI Assistant Functions
function openAIAssistant() {
    // Check if we have any agents available
    if (window.workflowManager && window.workflowManager.aiAgents.size > 0) {
        // Get the first available agent or a trading agent
        const agents = Array.from(window.workflowManager.aiAgents.values());
        const tradingAgent = agents.find(agent => agent.type === 'trading') || agents[0];

        if (window.llmChat) {
            window.llmChat.openAgentChat(tradingAgent.id);
        }
    } else {
        // Create a default AI assistant if no agents exist
        if (window.llmChat) {
            window.llmChat.openAgentChat('default_assistant');
        } else {
            showNotification('AI Assistant is loading...', 'info');
        }
    }
}

// Exchange Integration Functions
function refreshMarketData() {
    if (window.exchangeManager) {
        window.exchangeManager.updateMarketData();
        showNotification('Market data refreshed', 'success');
    } else {
        showNotification('Exchange manager not available', 'warning');
    }
}

function refreshArbitrage() {
    if (window.exchangeManager) {
        window.exchangeManager.loadArbitrageOpportunities();
        showNotification('Arbitrage opportunities updated', 'success');
    } else {
        showNotification('Exchange manager not available', 'warning');
    }
}