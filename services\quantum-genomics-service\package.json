{"name": "kontour-quantum-genomics-service", "version": "1.0.0", "description": "Quantum Computing and Genomics Integration Service for Advanced Blockchain Applications", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["quantum-computing", "genomics", "blockchain", "quantum-algorithms", "dna-sequencing", "bioinformatics", "quantum-cryptography", "kontour"], "author": "Kontour Coin Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "node-fetch": "^3.3.2", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "mathjs": "^12.2.0", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}