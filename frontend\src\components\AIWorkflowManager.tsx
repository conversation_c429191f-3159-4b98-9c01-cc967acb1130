import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON>H<PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from './ui/card';
import { <PERSON><PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from './ui/tabs';
import { 
  Play, 
  Pause, 
  Square, 
  Plus, 
  RefreshCw, 
  Brain, 
  Zap, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

interface WorkflowStep {
  id: string;
  name: string;
  type: string;
  ai_provider?: string;
  prompt?: string;
  parameters: Record<string, any>;
  dependencies: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: Record<string, any>;
  execution_time?: number;
  error?: string;
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  total_execution_time?: number;
  metadata?: Record<string, any>;
}

interface AIWorkflowManagerProps {
  apiUrl?: string;
  wsUrl?: string;
}

const AIWorkflowManager: React.FC<AIWorkflowManagerProps> = ({
  apiUrl = 'http://localhost:8080',
  wsUrl = 'ws://localhost:8080/ws'
}) => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [ws, setWs] = useState<WebSocket | null>(null);
  const [activeTab, setActiveTab] = useState('workflows');

  // WebSocket connection
  useEffect(() => {
    const connectWebSocket = () => {
      try {
        const websocket = new WebSocket(wsUrl);
        
        websocket.onopen = () => {
          console.log('WebSocket connected');
          setIsConnected(true);
          setWs(websocket);
        };
        
        websocket.onmessage = (event) => {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        };
        
        websocket.onclose = () => {
          console.log('WebSocket disconnected');
          setIsConnected(false);
          setWs(null);
          // Reconnect after 3 seconds
          setTimeout(connectWebSocket, 3000);
        };
        
        websocket.onerror = (error) => {
          console.error('WebSocket error:', error);
          setIsConnected(false);
        };
      } catch (error) {
        console.error('Failed to connect WebSocket:', error);
        setTimeout(connectWebSocket, 3000);
      }
    };

    connectWebSocket();

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, [wsUrl]);

  const handleWebSocketMessage = useCallback((message: any) => {
    switch (message.type) {
      case 'workflow_update':
        handleWorkflowUpdate(message);
        break;
      case 'connection_established':
        console.log('WebSocket connection established');
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }, []);

  const handleWorkflowUpdate = useCallback((message: any) => {
    const { workflow_id, event_type, data } = message;
    
    setWorkflows(prev => prev.map(workflow => {
      if (workflow.id === workflow_id) {
        switch (event_type) {
          case 'workflow_started':
            return { ...workflow, status: 'running', started_at: data.started_at };
          case 'workflow_completed':
            return { 
              ...workflow, 
              status: data.status, 
              completed_at: data.completed_at,
              total_execution_time: data.total_execution_time
            };
          case 'workflow_failed':
            return { 
              ...workflow, 
              status: 'failed', 
              completed_at: data.completed_at
            };
          case 'step_started':
            return {
              ...workflow,
              steps: workflow.steps.map(step => 
                step.id === data.step_id 
                  ? { ...step, status: 'running' }
                  : step
              )
            };
          case 'step_completed':
            return {
              ...workflow,
              steps: workflow.steps.map(step => 
                step.id === data.step_id 
                  ? { 
                      ...step, 
                      status: 'completed', 
                      result: data.result,
                      execution_time: data.execution_time
                    }
                  : step
              )
            };
          case 'step_failed':
            return {
              ...workflow,
              steps: workflow.steps.map(step => 
                step.id === data.step_id 
                  ? { ...step, status: 'failed', error: data.error }
                  : step
              )
            };
          default:
            return workflow;
        }
      }
      return workflow;
    }));

    // Update selected workflow if it's the one being updated
    if (selectedWorkflow && selectedWorkflow.id === workflow_id) {
      setSelectedWorkflow(prev => {
        if (!prev) return null;
        const updated = workflows.find(w => w.id === workflow_id);
        return updated || prev;
      });
    }
  }, [workflows, selectedWorkflow]);

  // Fetch workflows
  const fetchWorkflows = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${apiUrl}/workflows`);
      const data = await response.json();
      setWorkflows(data.workflows || []);
    } catch (error) {
      console.error('Failed to fetch workflows:', error);
    } finally {
      setLoading(false);
    }
  };

  // Execute workflow
  const executeWorkflow = async (workflowId: string) => {
    try {
      const response = await fetch(`${apiUrl}/workflows/${workflowId}/execute`, {
        method: 'POST'
      });
      
      if (response.ok) {
        console.log('Workflow execution started');
      } else {
        console.error('Failed to execute workflow');
      }
    } catch (error) {
      console.error('Error executing workflow:', error);
    }
  };

  // Create new workflow
  const createWorkflow = async (workflowData: any) => {
    try {
      const response = await fetch(`${apiUrl}/workflows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(workflowData)
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('Workflow created:', result);
        fetchWorkflows(); // Refresh the list
      } else {
        console.error('Failed to create workflow');
      }
    } catch (error) {
      console.error('Error creating workflow:', error);
    }
  };

  useEffect(() => {
    fetchWorkflows();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      pending: 'secondary',
      running: 'default',
      completed: 'success',
      failed: 'destructive',
      paused: 'warning'
    };
    
    return (
      <Badge variant={variants[status] as any}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status}</span>
      </Badge>
    );
  };

  const calculateProgress = (workflow: Workflow) => {
    const totalSteps = workflow.steps.length;
    const completedSteps = workflow.steps.filter(step => step.status === 'completed').length;
    return totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Brain className="h-8 w-8 text-purple-600" />
          <h1 className="text-3xl font-bold">AI Workflow Manager</h1>
          <div className="flex items-center space-x-2 ml-4">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button onClick={fetchWorkflows} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={() => setActiveTab('create')} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Workflow
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="create">Create</TabsTrigger>
          <TabsTrigger value="monitor">Monitor</TabsTrigger>
        </TabsList>

        <TabsContent value="workflows" className="space-y-4">
          {/* Workflows List */}
          <div className="grid gap-4">
            {loading ? (
              <div className="text-center py-8">Loading workflows...</div>
            ) : workflows.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No workflows found. Create your first workflow to get started.
              </div>
            ) : (
              workflows.map((workflow) => (
                <Card key={workflow.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{workflow.name}</CardTitle>
                      {getStatusBadge(workflow.status)}
                    </div>
                    <p className="text-sm text-gray-600">{workflow.description}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span>Progress: {workflow.steps.filter(s => s.status === 'completed').length}/{workflow.steps.length} steps</span>
                        <span>{Math.round(calculateProgress(workflow))}%</span>
                      </div>
                      <Progress value={calculateProgress(workflow)} className="h-2" />
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>Created: {new Date(workflow.created_at).toLocaleDateString()}</span>
                          {workflow.total_execution_time && (
                            <span>Duration: {workflow.total_execution_time.toFixed(2)}s</span>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedWorkflow(workflow)}
                          >
                            View Details
                          </Button>
                          {workflow.status === 'pending' && (
                            <Button
                              size="sm"
                              onClick={() => executeWorkflow(workflow.id)}
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Execute
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="create">
          <WorkflowCreator onCreateWorkflow={createWorkflow} />
        </TabsContent>

        <TabsContent value="monitor">
          <WorkflowMonitor workflows={workflows} />
        </TabsContent>
      </Tabs>

      {/* Workflow Details Modal */}
      {selectedWorkflow && (
        <WorkflowDetailsModal
          workflow={selectedWorkflow}
          onClose={() => setSelectedWorkflow(null)}
          onExecute={executeWorkflow}
        />
      )}
    </div>
  );
};

// Placeholder components - these would be implemented separately
const WorkflowCreator: React.FC<{ onCreateWorkflow: (data: any) => void }> = ({ onCreateWorkflow }) => (
  <Card>
    <CardHeader>
      <CardTitle>Create New Workflow</CardTitle>
    </CardHeader>
    <CardContent>
      <p>Workflow creation form would go here...</p>
    </CardContent>
  </Card>
);

const WorkflowMonitor: React.FC<{ workflows: Workflow[] }> = ({ workflows }) => (
  <Card>
    <CardHeader>
      <CardTitle>Workflow Monitor</CardTitle>
    </CardHeader>
    <CardContent>
      <p>Real-time monitoring dashboard would go here...</p>
    </CardContent>
  </Card>
);

const WorkflowDetailsModal: React.FC<{
  workflow: Workflow;
  onClose: () => void;
  onExecute: (id: string) => void;
}> = ({ workflow, onClose, onExecute }) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <Card className="w-full max-w-4xl max-h-[80vh] overflow-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{workflow.name}</CardTitle>
          <Button variant="outline" onClick={onClose}>Close</Button>
        </div>
      </CardHeader>
      <CardContent>
        <p>Detailed workflow view would go here...</p>
      </CardContent>
    </Card>
  </div>
);

export default AIWorkflowManager;
