{"name": "kontour-advanced-workflows-service", "version": "1.0.0", "description": "Comprehensive Technology Workflows for Web3, AI, Quantum Computing, IoT, Data Science, and Genomics", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["workflows", "web3", "blockchain", "quantum-computing", "ai", "neural-networks", "data-science", "iot", "big-data", "deep-learning", "machine-learning", "genomics", "bioinformatics", "accuracy-monitoring", "lab-experiments", "network-mining", "kontour"], "author": "Kontour Coin Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "node-fetch": "^3.3.2", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "lodash": "^4.17.21", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}