version: '3.8'

services:
  # Redis for caching and real-time data
  redis:
    image: redis:7-alpine
    container_name: kontour-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - kontour-network

  # AI Workflow Orchestrator
  ai-workflow-orchestrator:
    build:
      context: ./services/ai-workflow-orchestrator
      dockerfile: Dockerfile
    container_name: kontour-ai-workflow
    ports:
      - "8080:8080"
    environment:
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
    networks:
      - kontour-network
    restart: unless-stopped

  # Enhanced Real-time Workflow Service
  enhanced-realtime-workflow:
    build:
      context: ./services/enhanced-realtime-workflow
      dockerfile: Dockerfile
    container_name: kontour-realtime-workflow
    ports:
      - "8035:8035"
    environment:
      - REDIS_URL=redis://redis:6379
      - AI_WORKFLOW_SERVICE=http://ai-workflow-orchestrator:8080
    depends_on:
      - redis
      - ai-workflow-orchestrator
    volumes:
      - ./logs:/app/logs
    networks:
      - kontour-network
    restart: unless-stopped

  # AI-Enhanced Backend
  ai-enhanced-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kontour-ai-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - AI_WORKFLOW_SERVICE=http://ai-workflow-orchestrator:8080
      - REAL_TIME_SERVICE=http://enhanced-realtime-workflow:8035
    depends_on:
      - ai-workflow-orchestrator
      - enhanced-realtime-workflow
    volumes:
      - ./logs:/app/logs
    networks:
      - kontour-network
    restart: unless-stopped

volumes:
  redis_data:
    driver: local

networks:
  kontour-network:
    driver: bridge
