#!/usr/bin/env python3
"""
🚀 Enhanced Real-time Workflow Service
Advanced real-time processing with AI integration and blockchain synchronization
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

import websockets
import aioredis
import httpx
from fastapi import FastAPI, WebSocket, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Enhanced Real-time Workflow Service",
    description="Advanced real-time processing with AI integration",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
REDIS_URL = "redis://localhost:6379"
AI_WORKFLOW_SERVICE = "http://localhost:8080"
BLOCKCHAIN_SERVICE = "http://localhost:8030"

class EventType(Enum):
    WORKFLOW_UPDATE = "workflow_update"
    BLOCKCHAIN_EVENT = "blockchain_event"
    AI_RESPONSE = "ai_response"
    SYSTEM_ALERT = "system_alert"
    DATA_STREAM = "data_stream"

class Priority(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class RealtimeEvent:
    id: str
    type: EventType
    priority: Priority
    data: Dict[str, Any]
    timestamp: datetime
    source: str
    target: Optional[str] = None
    processed: bool = False
    retry_count: int = 0

@dataclass
class StreamSubscription:
    id: str
    client_id: str
    event_types: List[EventType]
    filters: Dict[str, Any]
    created_at: datetime
    last_activity: datetime

# Global state
connected_clients: Dict[str, WebSocket] = {}
active_subscriptions: Dict[str, StreamSubscription] = {}
event_queue: List[RealtimeEvent] = []
redis_client = None

# Redis connection
async def init_redis():
    global redis_client
    try:
        redis_client = await aioredis.from_url(REDIS_URL)
        logger.info("✅ Connected to Redis")
    except Exception as e:
        logger.error(f"❌ Failed to connect to Redis: {e}")

# Event processing
async def process_event(event: RealtimeEvent) -> bool:
    """Process a real-time event"""
    try:
        logger.info(f"Processing event: {event.type.value} from {event.source}")
        
        # Store event in Redis for persistence
        if redis_client:
            await redis_client.setex(
                f"event:{event.id}",
                3600,  # 1 hour TTL
                json.dumps(asdict(event), default=str)
            )
        
        # Route event based on type
        if event.type == EventType.WORKFLOW_UPDATE:
            await handle_workflow_update(event)
        elif event.type == EventType.BLOCKCHAIN_EVENT:
            await handle_blockchain_event(event)
        elif event.type == EventType.AI_RESPONSE:
            await handle_ai_response(event)
        elif event.type == EventType.SYSTEM_ALERT:
            await handle_system_alert(event)
        elif event.type == EventType.DATA_STREAM:
            await handle_data_stream(event)
        
        # Broadcast to subscribed clients
        await broadcast_event(event)
        
        event.processed = True
        return True
        
    except Exception as e:
        logger.error(f"Error processing event {event.id}: {e}")
        event.retry_count += 1
        return False

async def handle_workflow_update(event: RealtimeEvent):
    """Handle workflow update events"""
    workflow_data = event.data
    
    # Update workflow status in cache
    if redis_client:
        await redis_client.setex(
            f"workflow:{workflow_data.get('workflow_id')}",
            7200,  # 2 hours TTL
            json.dumps(workflow_data, default=str)
        )
    
    # Trigger dependent workflows if needed
    if workflow_data.get('status') == 'completed':
        await trigger_dependent_workflows(workflow_data.get('workflow_id'))

async def handle_blockchain_event(event: RealtimeEvent):
    """Handle blockchain events"""
    blockchain_data = event.data
    
    # Process blockchain transaction
    if blockchain_data.get('type') == 'transaction':
        await process_blockchain_transaction(blockchain_data)
    elif blockchain_data.get('type') == 'block':
        await process_new_block(blockchain_data)

async def handle_ai_response(event: RealtimeEvent):
    """Handle AI response events"""
    ai_data = event.data
    
    # Store AI response for analytics
    if redis_client:
        await redis_client.lpush(
            "ai_responses",
            json.dumps(ai_data, default=str)
        )
        await redis_client.ltrim("ai_responses", 0, 999)  # Keep last 1000

async def handle_system_alert(event: RealtimeEvent):
    """Handle system alert events"""
    alert_data = event.data
    
    # Log critical alerts
    if event.priority == Priority.CRITICAL:
        logger.critical(f"CRITICAL ALERT: {alert_data.get('message')}")
    
    # Store alert in Redis
    if redis_client:
        await redis_client.lpush(
            "system_alerts",
            json.dumps(asdict(event), default=str)
        )

async def handle_data_stream(event: RealtimeEvent):
    """Handle data stream events"""
    stream_data = event.data
    
    # Process streaming data
    await process_streaming_data(stream_data)

async def broadcast_event(event: RealtimeEvent):
    """Broadcast event to subscribed clients"""
    message = {
        "type": "realtime_event",
        "event": asdict(event),
        "timestamp": datetime.now().isoformat()
    }
    
    disconnected_clients = []
    
    for client_id, websocket in connected_clients.items():
        try:
            # Check if client is subscribed to this event type
            subscription = active_subscriptions.get(client_id)
            if subscription and event.type in subscription.event_types:
                # Apply filters if any
                if should_send_to_client(event, subscription.filters):
                    await websocket.send_json(message)
                    subscription.last_activity = datetime.now()
        except Exception as e:
            logger.error(f"Failed to send to client {client_id}: {e}")
            disconnected_clients.append(client_id)
    
    # Clean up disconnected clients
    for client_id in disconnected_clients:
        await cleanup_client(client_id)

def should_send_to_client(event: RealtimeEvent, filters: Dict[str, Any]) -> bool:
    """Check if event should be sent to client based on filters"""
    if not filters:
        return True
    
    # Apply priority filter
    if "min_priority" in filters:
        min_priority = Priority(filters["min_priority"])
        if event.priority.value < min_priority.value:
            return False
    
    # Apply source filter
    if "sources" in filters:
        if event.source not in filters["sources"]:
            return False
    
    # Apply data filters
    if "data_filters" in filters:
        for key, value in filters["data_filters"].items():
            if event.data.get(key) != value:
                return False
    
    return True

async def cleanup_client(client_id: str):
    """Clean up disconnected client"""
    if client_id in connected_clients:
        del connected_clients[client_id]
    if client_id in active_subscriptions:
        del active_subscriptions[client_id]
    logger.info(f"Cleaned up client: {client_id}")

# Helper functions
async def trigger_dependent_workflows(workflow_id: str):
    """Trigger workflows that depend on the completed workflow"""
    # This would query the AI workflow service for dependent workflows
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{AI_WORKFLOW_SERVICE}/workflows/dependencies/{workflow_id}")
            if response.status_code == 200:
                dependencies = response.json()
                for dep_workflow_id in dependencies.get("dependent_workflows", []):
                    await client.post(f"{AI_WORKFLOW_SERVICE}/workflows/{dep_workflow_id}/execute")
    except Exception as e:
        logger.error(f"Error triggering dependent workflows: {e}")

async def process_blockchain_transaction(transaction_data: Dict[str, Any]):
    """Process blockchain transaction"""
    # Validate and process transaction
    logger.info(f"Processing blockchain transaction: {transaction_data.get('hash')}")

async def process_new_block(block_data: Dict[str, Any]):
    """Process new blockchain block"""
    # Process new block
    logger.info(f"Processing new block: {block_data.get('number')}")

async def process_streaming_data(stream_data: Dict[str, Any]):
    """Process streaming data"""
    # Process real-time data streams
    logger.info(f"Processing stream data from: {stream_data.get('source')}")

# Pydantic Models
class CreateEventRequest(BaseModel):
    type: str
    priority: str
    data: Dict[str, Any]
    source: str
    target: Optional[str] = None

class SubscribeRequest(BaseModel):
    event_types: List[str]
    filters: Optional[Dict[str, Any]] = {}

# API Endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "enhanced-realtime-workflow",
        "timestamp": datetime.now().isoformat(),
        "connected_clients": len(connected_clients),
        "active_subscriptions": len(active_subscriptions),
        "pending_events": len([e for e in event_queue if not e.processed])
    }

@app.post("/events")
async def create_event(request: CreateEventRequest, background_tasks: BackgroundTasks):
    """Create a new real-time event"""
    event = RealtimeEvent(
        id=str(uuid.uuid4()),
        type=EventType(request.type),
        priority=Priority(request.priority),
        data=request.data,
        timestamp=datetime.now(),
        source=request.source,
        target=request.target
    )
    
    event_queue.append(event)
    background_tasks.add_task(process_event, event)
    
    return {
        "event_id": event.id,
        "status": "queued"
    }

@app.get("/events")
async def get_events(limit: int = 100, event_type: Optional[str] = None):
    """Get recent events"""
    events = event_queue[-limit:]
    
    if event_type:
        events = [e for e in events if e.type.value == event_type]
    
    return {
        "events": [asdict(event) for event in events],
        "total": len(events)
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket.accept()
    client_id = str(uuid.uuid4())
    connected_clients[client_id] = websocket
    
    logger.info(f"Client connected: {client_id}")
    
    try:
        # Send welcome message
        await websocket.send_json({
            "type": "connection_established",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        })
        
        # Handle incoming messages
        while True:
            try:
                data = await websocket.receive_json()
                await handle_websocket_message(client_id, data)
            except Exception as e:
                logger.error(f"WebSocket message error: {e}")
                break
    
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        await cleanup_client(client_id)

async def handle_websocket_message(client_id: str, data: Dict[str, Any]):
    """Handle incoming WebSocket messages"""
    message_type = data.get("type")
    
    if message_type == "subscribe":
        # Create subscription
        subscription = StreamSubscription(
            id=str(uuid.uuid4()),
            client_id=client_id,
            event_types=[EventType(et) for et in data.get("event_types", [])],
            filters=data.get("filters", {}),
            created_at=datetime.now(),
            last_activity=datetime.now()
        )
        active_subscriptions[client_id] = subscription
        
        # Send confirmation
        await connected_clients[client_id].send_json({
            "type": "subscription_confirmed",
            "subscription_id": subscription.id,
            "event_types": data.get("event_types", [])
        })
    
    elif message_type == "unsubscribe":
        if client_id in active_subscriptions:
            del active_subscriptions[client_id]
        
        await connected_clients[client_id].send_json({
            "type": "unsubscribed"
        })
    
    elif message_type == "ping":
        await connected_clients[client_id].send_json({
            "type": "pong",
            "timestamp": datetime.now().isoformat()
        })

# Background tasks
async def event_processor():
    """Background task to process events"""
    while True:
        try:
            # Process pending events
            pending_events = [e for e in event_queue if not e.processed and e.retry_count < 3]
            
            for event in pending_events:
                success = await process_event(event)
                if not success and event.retry_count >= 3:
                    logger.error(f"Failed to process event {event.id} after 3 retries")
                    event.processed = True  # Mark as processed to avoid infinite retries
            
            # Clean up old events
            cutoff_time = datetime.now().timestamp() - 3600  # 1 hour ago
            event_queue[:] = [e for e in event_queue if e.timestamp.timestamp() > cutoff_time]
            
            await asyncio.sleep(1)  # Process every second
        
        except Exception as e:
            logger.error(f"Error in event processor: {e}")
            await asyncio.sleep(5)

# Startup and shutdown
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    await init_redis()
    asyncio.create_task(event_processor())
    logger.info("Enhanced Real-time Workflow Service started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    if redis_client:
        await redis_client.close()
    logger.info("Enhanced Real-time Workflow Service shut down")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8035)
