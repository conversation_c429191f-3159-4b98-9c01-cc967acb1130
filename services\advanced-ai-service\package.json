{"name": "kontour-advanced-ai-service", "version": "1.0.0", "description": "Advanced AI Models Integration Service - Gemini, DALL-E, TensorFlow, Stable Diffusion, Sora", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["ai", "gemini", "dalle", "tensorflow", "stable-diffusion", "sora", "machine-learning", "image-generation", "video-generation", "kontour"], "author": "Kontour Coin Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "@tensorflow/tfjs-node": "^4.15.0", "node-fetch": "^3.3.2", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "sharp": "^0.33.0", "canvas": "^2.11.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}