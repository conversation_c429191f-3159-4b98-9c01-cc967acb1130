// Exchange Integration for Kontour Coin Platform
class ExchangeManager {
    constructor() {
        this.exchanges = new Map();
        this.marketData = new Map();
        this.arbitrageOpportunities = [];
        this.activeConnections = new Set();
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.loadExchanges();
        this.setupEventListeners();
        this.startRealTimeUpdates();
    }

    async loadExchanges() {
        try {
            const response = await fetch('http://localhost:8002/api/exchanges');
            if (response.ok) {
                const data = await response.json();
                data.exchanges.forEach(exchange => {
                    this.exchanges.set(exchange.id, exchange);
                });
                this.updateExchangesDisplay();
            } else {
                throw new Error('Failed to load exchanges');
            }
        } catch (error) {
            console.log('Loading mock exchange data...');
            this.loadMockExchanges();
        }
    }

    loadMockExchanges() {
        const mockExchanges = [
            {
                id: 'binance',
                name: 'Binance',
                status: 'active',
                supported_pairs: ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'SOL/USDT'],
                fees: { maker: 0.001, taker: 0.001 }
            },
            {
                id: 'coinbase',
                name: 'Coinbase Pro',
                status: 'active',
                supported_pairs: ['BTC/USD', 'ETH/USD', 'KONTOUR/USD'],
                fees: { maker: 0.005, taker: 0.005 }
            },
            {
                id: 'kraken',
                name: 'Kraken',
                status: 'active',
                supported_pairs: ['BTC/USD', 'ETH/USD', 'SOL/USD'],
                fees: { maker: 0.0016, taker: 0.0026 }
            }
        ];

        mockExchanges.forEach(exchange => {
            this.exchanges.set(exchange.id, exchange);
        });
        this.updateExchangesDisplay();
    }

    setupEventListeners() {
        // Exchange connection toggles
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('exchange-toggle')) {
                const exchangeId = e.target.dataset.exchangeId;
                this.toggleExchangeConnection(exchangeId);
            }
        });

        // Trading pair selection
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('pair-selector')) {
                const pair = e.target.value;
                this.loadMarketDataForPair(pair);
            }
        });

        // Arbitrage refresh
        const arbitrageRefreshBtn = document.getElementById('refresh-arbitrage');
        if (arbitrageRefreshBtn) {
            arbitrageRefreshBtn.addEventListener('click', () => {
                this.loadArbitrageOpportunities();
            });
        }
    }

    startRealTimeUpdates() {
        // Update market data every 5 seconds
        this.updateInterval = setInterval(() => {
            this.updateMarketData();
            this.loadArbitrageOpportunities();
        }, 5000);

        // WebSocket connection for real-time data
        this.connectWebSocket();
    }

    connectWebSocket() {
        try {
            const ws = new WebSocket('ws://localhost:8002');
            
            ws.onopen = () => {
                console.log('Connected to exchange WebSocket');
                ws.send(JSON.stringify({
                    type: 'subscribe_market_data',
                    payload: { pairs: ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT'] }
                }));
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'market_data_update') {
                    this.handleMarketDataUpdate(data.data);
                }
            };

            ws.onclose = () => {
                console.log('Exchange WebSocket disconnected');
                // Reconnect after 5 seconds
                setTimeout(() => this.connectWebSocket(), 5000);
            };

        } catch (error) {
            console.log('WebSocket connection failed, using polling');
        }
    }

    handleMarketDataUpdate(marketData) {
        marketData.forEach(data => {
            this.marketData.set(data.key, data);
        });
        this.updateMarketDataDisplay();
    }

    async updateMarketData() {
        const pairs = ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'SOL/USDT'];
        
        for (const pair of pairs) {
            try {
                const response = await fetch(`http://localhost:8002/api/market-data/${pair}`);
                if (response.ok) {
                    const data = await response.json();
                    this.marketData.set(pair, data);
                }
            } catch (error) {
                console.log(`Failed to update ${pair} data`);
            }
        }
        
        this.updateMarketDataDisplay();
    }

    async loadMarketDataForPair(pair) {
        try {
            const response = await fetch(`http://localhost:8002/api/market-data/${pair}`);
            if (response.ok) {
                const data = await response.json();
                this.displayPairDetails(pair, data);
            }
        } catch (error) {
            console.error(`Failed to load data for ${pair}:`, error);
        }
    }

    async loadArbitrageOpportunities() {
        try {
            const response = await fetch('http://localhost:8002/api/arbitrage');
            if (response.ok) {
                const data = await response.json();
                this.arbitrageOpportunities = data.opportunities;
                this.updateArbitrageDisplay();
            }
        } catch (error) {
            console.log('Failed to load arbitrage opportunities');
        }
    }

    toggleExchangeConnection(exchangeId) {
        if (this.activeConnections.has(exchangeId)) {
            this.activeConnections.delete(exchangeId);
        } else {
            this.activeConnections.add(exchangeId);
        }
        this.updateExchangesDisplay();
    }

    updateExchangesDisplay() {
        const exchangesContainer = document.getElementById('exchanges-list');
        if (!exchangesContainer) return;

        const exchangesArray = Array.from(this.exchanges.values());
        
        exchangesContainer.innerHTML = exchangesArray.map(exchange => `
            <div class="exchange-card" data-exchange="${exchange.id}">
                <div class="exchange-header">
                    <div class="exchange-info">
                        <h4>${exchange.name}</h4>
                        <span class="exchange-status status-${exchange.status}">${exchange.status}</span>
                    </div>
                    <div class="exchange-controls">
                        <button class="exchange-toggle ${this.activeConnections.has(exchange.id) ? 'connected' : 'disconnected'}" 
                                data-exchange-id="${exchange.id}">
                            ${this.activeConnections.has(exchange.id) ? 'Disconnect' : 'Connect'}
                        </button>
                    </div>
                </div>
                
                <div class="exchange-details">
                    <div class="exchange-fees">
                        <span class="fee-label">Maker:</span>
                        <span class="fee-value">${(exchange.fees.maker * 100).toFixed(3)}%</span>
                        <span class="fee-label">Taker:</span>
                        <span class="fee-value">${(exchange.fees.taker * 100).toFixed(3)}%</span>
                    </div>
                    
                    <div class="supported-pairs">
                        <span class="pairs-label">Pairs:</span>
                        <div class="pairs-list">
                            ${exchange.supported_pairs.slice(0, 3).map(pair => 
                                `<span class="pair-tag">${pair}</span>`
                            ).join('')}
                            ${exchange.supported_pairs.length > 3 ? 
                                `<span class="pair-tag more">+${exchange.supported_pairs.length - 3}</span>` : ''
                            }
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    updateMarketDataDisplay() {
        const marketDataContainer = document.getElementById('market-data-grid');
        if (!marketDataContainer) return;

        const pairs = ['BTC/USDT', 'ETH/USDT', 'KONTOUR/USDT', 'SOL/USDT'];
        
        marketDataContainer.innerHTML = pairs.map(pair => {
            const data = this.marketData.get(pair);
            if (!data) return '';

            const avgPrice = data.average_price || 0;
            const change24h = ((Math.random() - 0.5) * 0.1); // Mock 24h change
            const volume = Math.random() * 1000000;

            return `
                <div class="market-data-card" data-pair="${pair}">
                    <div class="pair-header">
                        <h4>${pair}</h4>
                        <span class="pair-price">$${avgPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 6})}</span>
                    </div>
                    
                    <div class="pair-metrics">
                        <div class="metric">
                            <span class="metric-label">24h Change</span>
                            <span class="metric-value ${change24h >= 0 ? 'positive' : 'negative'}">
                                ${change24h >= 0 ? '+' : ''}${(change24h * 100).toFixed(2)}%
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Volume</span>
                            <span class="metric-value">${volume.toLocaleString(undefined, {maximumFractionDigits: 0})}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Exchanges</span>
                            <span class="metric-value">${data.exchanges?.length || 0}</span>
                        </div>
                    </div>
                    
                    <div class="pair-actions">
                        <button class="btn btn-sm btn-primary" onclick="exchangeManager.viewPairDetails('${pair}')">
                            View Details
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="exchangeManager.tradePair('${pair}')">
                            Trade
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    updateArbitrageDisplay() {
        const arbitrageContainer = document.getElementById('arbitrage-opportunities');
        if (!arbitrageContainer) return;

        if (this.arbitrageOpportunities.length === 0) {
            arbitrageContainer.innerHTML = `
                <div class="no-opportunities">
                    <p>No arbitrage opportunities found at the moment.</p>
                    <button class="btn btn-primary" onclick="exchangeManager.loadArbitrageOpportunities()">
                        Refresh
                    </button>
                </div>
            `;
            return;
        }

        arbitrageContainer.innerHTML = this.arbitrageOpportunities.map(opp => `
            <div class="arbitrage-card">
                <div class="arbitrage-header">
                    <h4>${opp.pair}</h4>
                    <span class="profit-badge ${opp.profit_percent >= 0.5 ? 'high-profit' : 'low-profit'}">
                        +${opp.profit_percent.toFixed(2)}%
                    </span>
                </div>
                
                <div class="arbitrage-details">
                    <div class="exchange-prices">
                        <div class="buy-exchange">
                            <span class="exchange-label">Buy on ${opp.buy_exchange}</span>
                            <span class="exchange-price">$${opp.buy_price.toFixed(6)}</span>
                        </div>
                        <div class="sell-exchange">
                            <span class="exchange-label">Sell on ${opp.sell_exchange}</span>
                            <span class="exchange-price">$${opp.sell_price.toFixed(6)}</span>
                        </div>
                    </div>
                    
                    <div class="profit-info">
                        <span class="profit-amount">Profit: $${opp.profit_amount.toFixed(6)}</span>
                        <span class="profit-time">${new Date(opp.timestamp).toLocaleTimeString()}</span>
                    </div>
                </div>
                
                <div class="arbitrage-actions">
                    <button class="btn btn-sm btn-success" onclick="exchangeManager.executeArbitrage('${opp.pair}', '${opp.buy_exchange}', '${opp.sell_exchange}')">
                        Execute
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="exchangeManager.analyzeArbitrage('${opp.pair}')">
                        Analyze
                    </button>
                </div>
            </div>
        `).join('');
    }

    viewPairDetails(pair) {
        const data = this.marketData.get(pair);
        if (!data) return;

        // Show detailed modal or navigate to details page
        this.showPairDetailsModal(pair, data);
    }

    showPairDetailsModal(pair, data) {
        const modal = document.getElementById('pair-details-modal');
        if (!modal) return;

        const modalContent = modal.querySelector('.modal-content');
        modalContent.innerHTML = `
            <div class="modal-header">
                <h3>${pair} Market Details</h3>
                <button class="modal-close" onclick="this.closest('.modal').classList.remove('active')">×</button>
            </div>
            
            <div class="modal-body">
                <div class="pair-overview">
                    <div class="price-info">
                        <h4>Average Price: $${data.average_price.toFixed(6)}</h4>
                        <p>Across ${data.exchanges.length} exchanges</p>
                    </div>
                </div>
                
                <div class="exchange-prices">
                    <h5>Exchange Prices</h5>
                    <div class="prices-grid">
                        ${data.exchanges.map(ex => `
                            <div class="exchange-price-item">
                                <span class="exchange-name">${ex.exchange}</span>
                                <span class="price">$${ex.price.toFixed(6)}</span>
                                <span class="spread">Spread: ${((ex.ask - ex.bid) / ex.price * 100).toFixed(3)}%</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        modal.classList.add('active');
    }

    tradePair(pair) {
        // Open trading interface for the pair
        if (window.workflowManager) {
            window.workflowManager.showNotification(`Opening trading interface for ${pair}`, 'info');
        }
    }

    executeArbitrage(pair, buyExchange, sellExchange) {
        // Execute arbitrage trade
        if (window.workflowManager) {
            window.workflowManager.showNotification(
                `Executing arbitrage: Buy ${pair} on ${buyExchange}, Sell on ${sellExchange}`, 
                'success'
            );
        }
    }

    analyzeArbitrage(pair) {
        // Open LLM chat with arbitrage analysis
        if (window.llmChat) {
            window.llmChat.getArbitrageOpportunities();
        }
    }

    // Get exchange statistics
    getExchangeStats() {
        return {
            total_exchanges: this.exchanges.size,
            active_connections: this.activeConnections.size,
            supported_pairs: [...new Set(Array.from(this.exchanges.values()).flatMap(ex => ex.supported_pairs))].length,
            arbitrage_opportunities: this.arbitrageOpportunities.length
        };
    }

    // Cleanup
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Global exchange manager instance
let exchangeManager;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('exchanges-list') || document.getElementById('market-data-grid')) {
        exchangeManager = new ExchangeManager();
        window.exchangeManager = exchangeManager;
    }
});

// Global functions
function connectToExchange(exchangeId) {
    if (exchangeManager) {
        exchangeManager.toggleExchangeConnection(exchangeId);
    }
}

function refreshMarketData() {
    if (exchangeManager) {
        exchangeManager.updateMarketData();
    }
}

function refreshArbitrage() {
    if (exchangeManager) {
        exchangeManager.loadArbitrageOpportunities();
    }
}
