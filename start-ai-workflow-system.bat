@echo off
setlocal enabledelayedexpansion

REM 🚀 Kontour Coin AI Workflow System Startup Script (Windows)
REM Comprehensive startup for all AI-enhanced backend and frontend services

echo.
echo 🚀 Starting Kontour Coin AI Workflow System
echo ================================================

REM Configuration
set BACKEND_PORT=3001
set AI_WORKFLOW_PORT=8080
set REALTIME_PORT=8035
set FRONTEND_PORT=3000

REM Create logs directory
if not exist logs mkdir logs

REM Create PID tracking file
if exist .pids del .pids
echo. > .pids

echo 📋 Pre-flight checks...

REM Check if required directories exist
echo 📁 Checking directories...
if not exist "services\ai-workflow-orchestrator" (
    echo ❌ AI Workflow Orchestrator directory not found
    pause
    exit /b 1
)

if not exist "services\enhanced-realtime-workflow" (
    echo ❌ Enhanced Realtime Workflow directory not found
    pause
    exit /b 1
)

if not exist "backend" (
    echo ❌ Backend directory not found
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ Frontend directory not found
    pause
    exit /b 1
)

echo ✅ All pre-flight checks passed

REM Install dependencies if needed
echo.
echo 📦 Installing dependencies...

REM AI Workflow Orchestrator
if not exist "services\ai-workflow-orchestrator\venv" (
    echo 🐍 Setting up AI Workflow Orchestrator virtual environment...
    cd services\ai-workflow-orchestrator
    python -m venv venv
    call venv\Scripts\activate
    pip install -r requirements.txt
    deactivate
    cd ..\..
)

REM Enhanced Realtime Workflow
if not exist "services\enhanced-realtime-workflow\venv" (
    echo 🐍 Setting up Enhanced Realtime Workflow virtual environment...
    cd services\enhanced-realtime-workflow
    python -m venv venv
    call venv\Scripts\activate
    pip install -r requirements.txt
    deactivate
    cd ..\..
)

REM Backend dependencies
if not exist "backend\node_modules" (
    echo 📦 Installing backend dependencies...
    cd backend
    call npm install
    cd ..
)

REM Frontend dependencies
if not exist "frontend\node_modules" (
    echo 📦 Installing frontend dependencies...
    cd frontend
    call npm install
    cd ..
)

echo ✅ All dependencies installed

REM Start services
echo.
echo 🚀 Starting services...

REM 1. Start AI Workflow Orchestrator
echo 🔄 Starting AI Workflow Orchestrator...
cd services\ai-workflow-orchestrator
start "AI Workflow Orchestrator" cmd /c "call venv\Scripts\activate && python ai_workflow_orchestrator.py > ..\..\logs\ai-workflow-orchestrator.log 2>&1"
cd ..\..

REM Wait a bit for service to start
timeout /t 5 /nobreak > nul

REM 2. Start Enhanced Realtime Workflow Service
echo 🔄 Starting Enhanced Realtime Workflow...
cd services\enhanced-realtime-workflow
start "Enhanced Realtime Workflow" cmd /c "call venv\Scripts\activate && python realtime_workflow_service.py > ..\..\logs\enhanced-realtime-workflow.log 2>&1"
cd ..\..

REM Wait a bit for service to start
timeout /t 5 /nobreak > nul

REM 3. Start AI-Enhanced Backend
echo 🔄 Starting AI-Enhanced Backend...
cd backend
start "AI-Enhanced Backend" cmd /c "npm start > ..\logs\ai-enhanced-backend.log 2>&1"
cd ..

REM Wait a bit for service to start
timeout /t 5 /nobreak > nul

REM 4. Start Frontend
echo 🔄 Starting Frontend...
cd frontend
start "Frontend" cmd /c "npm start > ..\logs\frontend.log 2>&1"
cd ..

REM Wait for all services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 15 /nobreak > nul

echo.
echo 🎉 All services started successfully!
echo ================================================
echo 📊 Service URLs:
echo    • Frontend Dashboard:        http://localhost:%FRONTEND_PORT%
echo    • AI-Enhanced Backend:       http://localhost:%BACKEND_PORT%
echo    • AI Workflow Orchestrator:  http://localhost:%AI_WORKFLOW_PORT%
echo    • Enhanced Realtime Service: http://localhost:%REALTIME_PORT%
echo ================================================
echo 📋 Health Check URLs:
echo    • Backend Health:    http://localhost:%BACKEND_PORT%/health
echo    • AI Workflow Health: http://localhost:%AI_WORKFLOW_PORT%/health
echo    • Realtime Health:   http://localhost:%REALTIME_PORT%/health
echo ================================================
echo 📝 Log Files:
echo    • AI Workflow:       logs\ai-workflow-orchestrator.log
echo    • Realtime Service:  logs\enhanced-realtime-workflow.log
echo    • Backend:           logs\ai-enhanced-backend.log
echo    • Frontend:          logs\frontend.log
echo ================================================

echo.
echo 🎯 System Features Available:
echo    ✅ AI Workflow Management (ChatGPT, Claude, Gemini, Deepseek)
echo    ✅ Real-time Event Processing
echo    ✅ WebSocket Live Updates
echo    ✅ Comprehensive Dashboard
echo    ✅ System Health Monitoring
echo    ✅ Blockchain Integration
echo    ✅ Multi-AI Provider Support

echo.
echo 💡 Tips:
echo    • Check logs\ directory for detailed logs
echo    • Visit the dashboard to create and manage workflows
echo    • API documentation available at each service's /docs endpoint
echo    • Close this window or press Ctrl+C to stop monitoring

echo.
echo 🚀 Kontour Coin AI Workflow System is now running!
echo.

REM Open the dashboard in default browser
echo 🌐 Opening dashboard in browser...
start http://localhost:%FRONTEND_PORT%

echo Press any key to stop all services...
pause > nul

REM Cleanup - kill all related processes
echo.
echo 🛑 Shutting down services...
taskkill /f /im "python.exe" 2>nul
taskkill /f /im "node.exe" 2>nul
taskkill /f /im "npm.cmd" 2>nul

echo ✅ All services stopped
echo.
pause
