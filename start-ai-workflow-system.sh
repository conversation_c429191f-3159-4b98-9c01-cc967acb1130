#!/bin/bash

# 🚀 Kontour Coin AI Workflow System Startup Script
# Comprehensive startup for all AI-enhanced backend and frontend services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=3001
AI_WORKFLOW_PORT=8080
REALTIME_PORT=8035
FRONTEND_PORT=3000

echo -e "${PURPLE}🚀 Starting Kontour Coin AI Workflow System${NC}"
echo -e "${CYAN}================================================${NC}"

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Port $port is already in use${NC}"
        return 1
    fi
    return 0
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${BLUE}⏳ Waiting for $service_name to be ready...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready!${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}   Attempt $attempt/$max_attempts - waiting for $service_name...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to start within timeout${NC}"
    return 1
}

# Function to start service in background
start_service() {
    local command=$1
    local service_name=$2
    local log_file=$3
    
    echo -e "${BLUE}🔄 Starting $service_name...${NC}"
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    # Start service in background
    eval "$command" > "logs/$log_file" 2>&1 &
    local pid=$!
    
    echo -e "${GREEN}✅ $service_name started (PID: $pid)${NC}"
    echo $pid >> .pids
    
    return 0
}

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🛑 Shutting down services...${NC}"
    
    if [ -f .pids ]; then
        while read pid; do
            if kill -0 $pid 2>/dev/null; then
                echo -e "${BLUE}   Stopping process $pid${NC}"
                kill $pid 2>/dev/null || true
            fi
        done < .pids
        rm -f .pids
    fi
    
    echo -e "${GREEN}✅ All services stopped${NC}"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Create PID file
rm -f .pids
touch .pids

echo -e "${BLUE}📋 Pre-flight checks...${NC}"

# Check required ports
echo -e "${BLUE}🔍 Checking port availability...${NC}"
check_port $BACKEND_PORT || exit 1
check_port $AI_WORKFLOW_PORT || exit 1
check_port $REALTIME_PORT || exit 1
check_port $FRONTEND_PORT || exit 1

# Check if required directories exist
echo -e "${BLUE}📁 Checking directories...${NC}"
if [ ! -d "services/ai-workflow-orchestrator" ]; then
    echo -e "${RED}❌ AI Workflow Orchestrator directory not found${NC}"
    exit 1
fi

if [ ! -d "services/enhanced-realtime-workflow" ]; then
    echo -e "${RED}❌ Enhanced Realtime Workflow directory not found${NC}"
    exit 1
fi

if [ ! -d "backend" ]; then
    echo -e "${RED}❌ Backend directory not found${NC}"
    exit 1
fi

if [ ! -d "frontend" ]; then
    echo -e "${RED}❌ Frontend directory not found${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All pre-flight checks passed${NC}"

# Install dependencies if needed
echo -e "\n${BLUE}📦 Installing dependencies...${NC}"

# AI Workflow Orchestrator
if [ ! -d "services/ai-workflow-orchestrator/venv" ]; then
    echo -e "${BLUE}🐍 Setting up AI Workflow Orchestrator virtual environment...${NC}"
    cd services/ai-workflow-orchestrator
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    deactivate
    cd ../..
fi

# Enhanced Realtime Workflow
if [ ! -d "services/enhanced-realtime-workflow/venv" ]; then
    echo -e "${BLUE}🐍 Setting up Enhanced Realtime Workflow virtual environment...${NC}"
    cd services/enhanced-realtime-workflow
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    deactivate
    cd ../..
fi

# Backend dependencies
if [ ! -d "backend/node_modules" ]; then
    echo -e "${BLUE}📦 Installing backend dependencies...${NC}"
    cd backend
    npm install
    cd ..
fi

# Frontend dependencies
if [ ! -d "frontend/node_modules" ]; then
    echo -e "${BLUE}📦 Installing frontend dependencies...${NC}"
    cd frontend
    npm install
    cd ..
fi

echo -e "${GREEN}✅ All dependencies installed${NC}"

# Start services
echo -e "\n${PURPLE}🚀 Starting services...${NC}"

# 1. Start AI Workflow Orchestrator
start_service \
    "cd services/ai-workflow-orchestrator && source venv/bin/activate && python ai_workflow_orchestrator.py" \
    "AI Workflow Orchestrator" \
    "ai-workflow-orchestrator.log"

# Wait for AI Workflow Orchestrator to be ready
wait_for_service "http://localhost:$AI_WORKFLOW_PORT/health" "AI Workflow Orchestrator"

# 2. Start Enhanced Realtime Workflow Service
start_service \
    "cd services/enhanced-realtime-workflow && source venv/bin/activate && python realtime_workflow_service.py" \
    "Enhanced Realtime Workflow" \
    "enhanced-realtime-workflow.log"

# Wait for Realtime Workflow to be ready
wait_for_service "http://localhost:$REALTIME_PORT/health" "Enhanced Realtime Workflow"

# 3. Start AI-Enhanced Backend
start_service \
    "cd backend && npm start" \
    "AI-Enhanced Backend" \
    "ai-enhanced-backend.log"

# Wait for backend to be ready
wait_for_service "http://localhost:$BACKEND_PORT/health" "AI-Enhanced Backend"

# 4. Start Frontend
start_service \
    "cd frontend && npm start" \
    "Frontend" \
    "frontend.log"

# Wait for frontend to be ready
wait_for_service "http://localhost:$FRONTEND_PORT" "Frontend"

echo -e "\n${GREEN}🎉 All services started successfully!${NC}"
echo -e "${CYAN}================================================${NC}"
echo -e "${BLUE}📊 Service URLs:${NC}"
echo -e "${GREEN}   • Frontend Dashboard:        http://localhost:$FRONTEND_PORT${NC}"
echo -e "${GREEN}   • AI-Enhanced Backend:       http://localhost:$BACKEND_PORT${NC}"
echo -e "${GREEN}   • AI Workflow Orchestrator:  http://localhost:$AI_WORKFLOW_PORT${NC}"
echo -e "${GREEN}   • Enhanced Realtime Service: http://localhost:$REALTIME_PORT${NC}"
echo -e "${CYAN}================================================${NC}"
echo -e "${BLUE}📋 Health Check URLs:${NC}"
echo -e "${GREEN}   • Backend Health:    http://localhost:$BACKEND_PORT/health${NC}"
echo -e "${GREEN}   • AI Workflow Health: http://localhost:$AI_WORKFLOW_PORT/health${NC}"
echo -e "${GREEN}   • Realtime Health:   http://localhost:$REALTIME_PORT/health${NC}"
echo -e "${CYAN}================================================${NC}"
echo -e "${BLUE}📝 Log Files:${NC}"
echo -e "${GREEN}   • AI Workflow:       logs/ai-workflow-orchestrator.log${NC}"
echo -e "${GREEN}   • Realtime Service:  logs/enhanced-realtime-workflow.log${NC}"
echo -e "${GREEN}   • Backend:           logs/ai-enhanced-backend.log${NC}"
echo -e "${GREEN}   • Frontend:          logs/frontend.log${NC}"
echo -e "${CYAN}================================================${NC}"

echo -e "\n${PURPLE}🎯 System Features Available:${NC}"
echo -e "${GREEN}   ✅ AI Workflow Management (ChatGPT, Claude, Gemini, Deepseek)${NC}"
echo -e "${GREEN}   ✅ Real-time Event Processing${NC}"
echo -e "${GREEN}   ✅ WebSocket Live Updates${NC}"
echo -e "${GREEN}   ✅ Comprehensive Dashboard${NC}"
echo -e "${GREEN}   ✅ System Health Monitoring${NC}"
echo -e "${GREEN}   ✅ Blockchain Integration${NC}"
echo -e "${GREEN}   ✅ Multi-AI Provider Support${NC}"

echo -e "\n${YELLOW}💡 Tips:${NC}"
echo -e "${BLUE}   • Use Ctrl+C to stop all services${NC}"
echo -e "${BLUE}   • Check logs/ directory for detailed logs${NC}"
echo -e "${BLUE}   • Visit the dashboard to create and manage workflows${NC}"
echo -e "${BLUE}   • API documentation available at each service's /docs endpoint${NC}"

echo -e "\n${GREEN}🚀 Kontour Coin AI Workflow System is now running!${NC}"
echo -e "${PURPLE}Press Ctrl+C to stop all services...${NC}"

# Keep script running and monitor services
while true; do
    sleep 10
    
    # Check if all services are still running
    if [ -f .pids ]; then
        dead_services=0
        while read pid; do
            if ! kill -0 $pid 2>/dev/null; then
                dead_services=$((dead_services + 1))
            fi
        done < .pids
        
        if [ $dead_services -gt 0 ]; then
            echo -e "${RED}⚠️  Some services have stopped. Check logs for details.${NC}"
        fi
    fi
done
