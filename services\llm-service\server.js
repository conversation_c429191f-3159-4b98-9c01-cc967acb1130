const express = require('express');
const cors = require('cors');
const WebSocket = require('ws');
const { createServer } = require('http');

const app = express();
const server = createServer(app);
const wss = new WebSocket.Server({ server });

// Middleware
app.use(cors());
app.use(express.json());

// LLM Model Configuration
const LLM_CONFIG = {
    model: 'kontour-ai-v1',
    max_tokens: 2048,
    temperature: 0.7,
    top_p: 0.9,
    frequency_penalty: 0.1,
    presence_penalty: 0.1
};

// LLM Context Storage
const conversationHistory = new Map();
const modelCache = new Map();
const agentPersonalities = new Map();

// Initialize AI Agent Personalities
function initializeAgentPersonalities() {
    const personalities = {
        'trading': {
            name: 'Alpha Trader',
            personality: 'Analytical, risk-aware, data-driven trading specialist',
            expertise: ['technical analysis', 'market trends', 'risk management', 'portfolio optimization'],
            communication_style: 'Professional, concise, numbers-focused',
            decision_making: 'Evidence-based with calculated risk assessment'
        },
        'analysis': {
            name: 'Market Sage',
            personality: 'Insightful, comprehensive, forward-thinking analyst',
            expertise: ['market research', 'trend prediction', 'fundamental analysis', 'sentiment analysis'],
            communication_style: 'Detailed, educational, context-rich',
            decision_making: 'Holistic analysis with multiple data sources'
        },
        'optimization': {
            name: 'Portfolio Optimizer',
            personality: 'Systematic, efficiency-focused, mathematically precise',
            expertise: ['portfolio theory', 'asset allocation', 'risk-return optimization', 'rebalancing'],
            communication_style: 'Structured, logical, optimization-focused',
            decision_making: 'Mathematical models with efficiency maximization'
        },
        'security': {
            name: 'Guardian Protocol',
            personality: 'Vigilant, protective, security-first mindset',
            expertise: ['threat detection', 'risk assessment', 'security protocols', 'compliance'],
            communication_style: 'Alert, precise, security-focused',
            decision_making: 'Security-first with comprehensive threat analysis'
        }
    };

    Object.entries(personalities).forEach(([type, personality]) => {
        agentPersonalities.set(type, personality);
    });
}

// LLM Processing Functions
async function processLLMRequest(prompt, context = {}) {
    try {
        const {
            agent_type = 'general',
            conversation_id = 'default',
            user_id = 'anonymous',
            task_type = 'general',
            market_data = {},
            portfolio_data = {}
        } = context;

        // Get conversation history
        const history = conversationHistory.get(conversation_id) || [];
        
        // Build enhanced prompt with context
        const enhancedPrompt = buildEnhancedPrompt(prompt, {
            agent_type,
            task_type,
            market_data,
            portfolio_data,
            history: history.slice(-10) // Last 10 messages
        });

        // Simulate LLM processing (replace with actual LLM API call)
        const response = await simulateLLMResponse(enhancedPrompt, agent_type);

        // Store conversation
        history.push({
            timestamp: Date.now(),
            user_message: prompt,
            ai_response: response.content,
            context: { agent_type, task_type }
        });
        conversationHistory.set(conversation_id, history);

        return {
            success: true,
            response: response.content,
            metadata: {
                model: LLM_CONFIG.model,
                tokens_used: response.tokens_used,
                processing_time: response.processing_time,
                confidence: response.confidence,
                agent_type,
                task_type
            }
        };

    } catch (error) {
        console.error('LLM Processing Error:', error);
        return {
            success: false,
            error: error.message,
            fallback_response: generateFallbackResponse(context.agent_type)
        };
    }
}

function buildEnhancedPrompt(userPrompt, context) {
    const { agent_type, task_type, market_data, portfolio_data, history } = context;
    const personality = agentPersonalities.get(agent_type) || agentPersonalities.get('trading');

    let enhancedPrompt = `
You are ${personality.name}, a ${personality.personality}.

Your expertise includes: ${personality.expertise.join(', ')}.
Your communication style: ${personality.communication_style}.
Your decision-making approach: ${personality.decision_making}.

Current Context:
- Task Type: ${task_type}
- Agent Type: ${agent_type}
`;

    // Add market data context
    if (Object.keys(market_data).length > 0) {
        enhancedPrompt += `
Market Data:
- BTC Price: $${market_data.btc_price || '45,000'}
- ETH Price: $${market_data.eth_price || '3,200'}
- Market Trend: ${market_data.trend || 'Bullish'}
- Volume: ${market_data.volume || 'High'}
- Volatility: ${market_data.volatility || 'Medium'}
`;
    }

    // Add portfolio context
    if (Object.keys(portfolio_data).length > 0) {
        enhancedPrompt += `
Portfolio Context:
- Total Value: $${portfolio_data.total_value || '50,000'}
- BTC Allocation: ${portfolio_data.btc_allocation || '40'}%
- ETH Allocation: ${portfolio_data.eth_allocation || '30'}%
- KONTOUR Allocation: ${portfolio_data.kontour_allocation || '20'}%
- Cash: ${portfolio_data.cash_allocation || '10'}%
`;
    }

    // Add conversation history
    if (history.length > 0) {
        enhancedPrompt += `
Recent Conversation:
${history.slice(-3).map(h => `User: ${h.user_message}\nAI: ${h.ai_response}`).join('\n')}
`;
    }

    enhancedPrompt += `
User Request: ${userPrompt}

Please provide a comprehensive response that:
1. Addresses the user's specific request
2. Incorporates relevant market and portfolio context
3. Provides actionable insights or recommendations
4. Maintains your personality and expertise focus
5. Uses your characteristic communication style

Response:`;

    return enhancedPrompt;
}

async function simulateLLMResponse(prompt, agentType) {
    // Simulate processing time
    const processingTime = 800 + Math.random() * 1200;
    await new Promise(resolve => setTimeout(resolve, processingTime));

    const personality = agentPersonalities.get(agentType) || agentPersonalities.get('trading');
    
    // Generate contextual responses based on agent type and prompt analysis
    const response = generateContextualResponse(prompt, agentType, personality);
    
    return {
        content: response,
        tokens_used: Math.floor(response.length / 4), // Approximate token count
        processing_time: processingTime,
        confidence: 0.85 + Math.random() * 0.1 // 85-95% confidence
    };
}

function generateContextualResponse(prompt, agentType, personality) {
    const promptLower = prompt.toLowerCase();
    
    // Trading Agent Responses
    if (agentType === 'trading') {
        if (promptLower.includes('buy') || promptLower.includes('sell')) {
            return `Based on current market analysis, I recommend a ${Math.random() > 0.5 ? 'gradual' : 'strategic'} approach. 

Key factors to consider:
• Technical indicators show ${['bullish', 'bearish', 'neutral'][Math.floor(Math.random() * 3)]} momentum
• Risk-reward ratio: ${(1 + Math.random() * 2).toFixed(1)}:1
• Recommended position size: ${(5 + Math.random() * 15).toFixed(1)}% of portfolio
• Stop-loss level: ${(3 + Math.random() * 5).toFixed(1)}% below entry

Market sentiment appears ${['optimistic', 'cautious', 'volatile'][Math.floor(Math.random() * 3)]}. I suggest monitoring volume and price action before executing.`;
        }
        
        if (promptLower.includes('market') || promptLower.includes('analysis')) {
            return `Current market analysis indicates:

📊 Technical Overview:
• Trend: ${['Uptrend', 'Downtrend', 'Sideways'][Math.floor(Math.random() * 3)]} with ${['strong', 'moderate', 'weak'][Math.floor(Math.random() * 3)]} momentum
• Support: $${(40000 + Math.random() * 5000).toFixed(0)}
• Resistance: $${(48000 + Math.random() * 7000).toFixed(0)}
• RSI: ${(30 + Math.random() * 40).toFixed(1)} (${Math.random() > 0.5 ? 'Oversold' : 'Neutral'})

🎯 Trading Opportunities:
• Short-term: ${['Scalping opportunities', 'Range trading', 'Breakout potential'][Math.floor(Math.random() * 3)]}
• Medium-term: ${['Trend continuation', 'Reversal setup', 'Consolidation phase'][Math.floor(Math.random() * 3)]}

Risk assessment: ${['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)]} volatility expected.`;
        }
    }
    
    // Analysis Agent Responses
    if (agentType === 'analysis') {
        if (promptLower.includes('predict') || promptLower.includes('forecast')) {
            return `Based on comprehensive market analysis:

🔮 Price Predictions:
• Next 24h: ${(Math.random() > 0.5 ? '+' : '-')}${(Math.random() * 5).toFixed(1)}%
• Next 7 days: ${(Math.random() > 0.5 ? '+' : '-')}${(Math.random() * 15).toFixed(1)}%
• Next 30 days: ${(Math.random() > 0.5 ? '+' : '-')}${(Math.random() * 25).toFixed(1)}%

📈 Key Drivers:
• Institutional adoption: ${['Increasing', 'Stable', 'Decreasing'][Math.floor(Math.random() * 3)]}
• Regulatory environment: ${['Positive', 'Neutral', 'Uncertain'][Math.floor(Math.random() * 3)]}
• Market sentiment: ${['Bullish', 'Bearish', 'Mixed'][Math.floor(Math.random() * 3)]}

Confidence level: ${(75 + Math.random() * 20).toFixed(1)}%`;
        }
    }
    
    // Default intelligent response
    return `I understand you're asking about ${promptLower.includes('kontour') ? 'KONTOUR' : 'market'} ${promptLower.includes('price') ? 'pricing' : 'analysis'}. 

As ${personality.name}, I analyze this through my ${personality.decision_making.toLowerCase()} approach:

Key insights:
• Current market conditions favor ${['conservative', 'moderate', 'aggressive'][Math.floor(Math.random() * 3)]} strategies
• Risk factors: ${['Volatility', 'Liquidity', 'Regulatory'][Math.floor(Math.random() * 3)]} considerations
• Opportunity score: ${(6 + Math.random() * 4).toFixed(1)}/10

My recommendation: ${['Monitor closely', 'Consider gradual entry', 'Wait for confirmation'][Math.floor(Math.random() * 3)]} based on your risk tolerance and investment timeline.

Would you like me to elaborate on any specific aspect?`;
}

function generateFallbackResponse(agentType) {
    const responses = {
        'trading': 'I apologize, but I\'m experiencing technical difficulties. As a trading agent, I recommend exercising caution and consulting multiple sources before making any trading decisions.',
        'analysis': 'I\'m currently unable to process your request fully. Please try again, and I\'ll provide you with comprehensive market analysis.',
        'optimization': 'System temporarily unavailable. For portfolio optimization, consider maintaining current allocations until I can provide detailed recommendations.',
        'security': 'Security protocols are functioning normally. Please retry your request for detailed security analysis.'
    };
    
    return responses[agentType] || 'I apologize for the temporary service interruption. Please try your request again.';
}

// API Endpoints
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'llm-service',
        model: LLM_CONFIG.model,
        timestamp: new Date().toISOString()
    });
});

app.post('/api/llm/chat', async (req, res) => {
    try {
        const { prompt, context = {} } = req.body;
        
        if (!prompt) {
            return res.status(400).json({ error: 'Prompt is required' });
        }
        
        const result = await processLLMRequest(prompt, context);
        res.json(result);
        
    } catch (error) {
        console.error('Chat API Error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.post('/api/llm/agent-chat', async (req, res) => {
    try {
        const { agent_id, message, context = {} } = req.body;
        
        const result = await processLLMRequest(message, {
            ...context,
            conversation_id: agent_id,
            agent_type: context.agent_type || 'trading'
        });
        
        res.json(result);
        
    } catch (error) {
        console.error('Agent Chat API Error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.get('/api/llm/conversations/:conversationId', (req, res) => {
    try {
        const { conversationId } = req.params;
        const history = conversationHistory.get(conversationId) || [];
        
        res.json({
            success: true,
            conversation_id: conversationId,
            message_count: history.length,
            messages: history
        });
        
    } catch (error) {
        console.error('Conversation API Error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// WebSocket for real-time LLM interactions
wss.on('connection', (ws) => {
    console.log('LLM WebSocket client connected');
    
    ws.on('message', async (data) => {
        try {
            const request = JSON.parse(data);
            const { type, payload } = request;
            
            if (type === 'llm_chat') {
                const result = await processLLMRequest(payload.prompt, payload.context);
                ws.send(JSON.stringify({
                    type: 'llm_response',
                    payload: result
                }));
            }
            
        } catch (error) {
            console.error('WebSocket LLM Error:', error);
            ws.send(JSON.stringify({
                type: 'error',
                payload: { error: error.message }
            }));
        }
    });
    
    ws.on('close', () => {
        console.log('LLM WebSocket client disconnected');
    });
});

// Initialize
initializeAgentPersonalities();

const PORT = process.env.PORT || 8001;
server.listen(PORT, () => {
    console.log(`🧠 LLM Service running on port ${PORT}`);
    console.log(`🤖 AI Agent personalities initialized: ${agentPersonalities.size} types`);
});

module.exports = app;
