// Comprehensive Technology Workflows Manager
class ComprehensiveWorkflowsManager {
    constructor() {
        this.activeWorkflows = new Map();
        this.accuracyMetrics = new Map();
        this.networkStatus = new Map();
        this.labEnvironments = new Map();
        this.websocket = null;
        this.technologies = {};
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.loadTechnologies();
        this.setupEventListeners();
        this.loadWorkflowDashboard();
        this.startRealTimeMonitoring();
    }

    connectWebSocket() {
        try {
            this.websocket = new WebSocket('ws://localhost:8008');
            
            this.websocket.onopen = () => {
                console.log('Comprehensive Workflows WebSocket connected');
                this.updateConnectionStatus('connected');
            };

            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };

            this.websocket.onclose = () => {
                console.log('Comprehensive Workflows WebSocket disconnected');
                this.updateConnectionStatus('disconnected');
                setTimeout(() => this.connectWebSocket(), 5000);
            };
        } catch (error) {
            console.log('WebSocket connection failed, using fallback mode');
            this.updateConnectionStatus('fallback');
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'workflows_connected':
                this.loadTechnologies();
                break;
            case 'workflow_update':
                this.updateWorkflowProgress(data);
                break;
            case 'network_status_update':
                this.updateNetworkStatus(data.network_status);
                this.updateAccuracyMetrics(data.accuracy_metrics);
                break;
        }
    }

    async loadTechnologies() {
        try {
            const response = await fetch('http://localhost:8008/api/technologies');
            if (response.ok) {
                const data = await response.json();
                this.technologies = data.technologies;
                this.updateTechnologiesDisplay();
            }
        } catch (error) {
            console.log('Loading mock technologies data');
            this.loadMockTechnologies();
        }
    }

    loadMockTechnologies() {
        this.technologies = {
            web3_blockchain: {
                name: 'Web3 & Blockchain Workflows',
                accuracy: 0.9987,
                workflows: {
                    smart_contract_deployment: { accuracy: 0.9995, complexity: 'high' },
                    defi_protocol_integration: { accuracy: 0.9992, complexity: 'expert' },
                    cross_chain_bridge: { accuracy: 0.9989, complexity: 'expert' },
                    nft_marketplace: { accuracy: 0.9993, complexity: 'intermediate' }
                }
            },
            quantum_computing: {
                name: 'Quantum Computing Lab',
                accuracy: 0.9985,
                workflows: {
                    quantum_algorithm_execution: { accuracy: 0.9987, complexity: 'expert' },
                    quantum_machine_learning: { accuracy: 0.9983, complexity: 'expert' },
                    quantum_cryptography: { accuracy: 0.9991, complexity: 'expert' },
                    quantum_simulation: { accuracy: 0.9986, complexity: 'expert' }
                }
            },
            ai_neural_networks: {
                name: 'AI & Neural Networks Lab',
                accuracy: 0.9982,
                workflows: {
                    deep_learning_training: { accuracy: 0.9984, complexity: 'high' },
                    neural_architecture_search: { accuracy: 0.9979, complexity: 'expert' },
                    transfer_learning: { accuracy: 0.9988, complexity: 'intermediate' },
                    reinforcement_learning: { accuracy: 0.9981, complexity: 'expert' }
                }
            }
        };
        this.updateTechnologiesDisplay();
    }

    async executeWorkflow(technology, workflowType, parameters = {}) {
        try {
            this.showNotification(`Starting ${technology} workflow: ${workflowType}`, 'info');
            
            const response = await fetch('http://localhost:8008/api/workflow/execute', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    technology,
                    workflow_type: workflowType,
                    parameters
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification(`Workflow started successfully with ${(result.accuracy_target * 100).toFixed(2)}% target accuracy`, 'success');
                this.addWorkflowToActive(result.workflow_id, technology, workflowType, result);
                return result;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            this.showNotification(`Workflow execution failed: ${error.message}`, 'error');
            return { success: false, error: error.message };
        }
    }

    addWorkflowToActive(workflowId, technology, workflowType, result) {
        const workflow = {
            id: workflowId,
            technology,
            workflow_type: workflowType,
            status: 'running',
            progress: 0,
            accuracy: 0,
            lab_environment: result.lab_environment,
            estimated_completion: result.estimated_completion,
            accuracy_target: result.accuracy_target,
            created_at: Date.now()
        };
        
        this.activeWorkflows.set(workflowId, workflow);
        this.updateActiveWorkflowsDisplay();
    }

    updateWorkflowProgress(data) {
        const workflow = this.activeWorkflows.get(data.workflow_id);
        if (workflow) {
            workflow.status = data.status;
            workflow.progress = (data.current_step / data.steps.length) * 100;
            workflow.accuracy = data.overall_accuracy;
            workflow.steps = data.steps;
            
            this.activeWorkflows.set(data.workflow_id, workflow);
            this.updateActiveWorkflowsDisplay();
            this.updateWorkflowCard(data.workflow_id, workflow);
        }
    }

    updateTechnologiesDisplay() {
        const container = document.getElementById('technologies-overview');
        if (!container) return;

        container.innerHTML = `
            <div class="technologies-grid">
                ${Object.entries(this.technologies).map(([techId, tech]) => `
                    <div class="technology-card professional-card" data-technology="${techId}">
                        <div class="tech-header">
                            <div class="tech-icon">${this.getTechnologyIcon(techId)}</div>
                            <h3>${tech.name}</h3>
                            <div class="tech-accuracy">${(tech.accuracy * 100).toFixed(2)}% Accuracy</div>
                        </div>
                        
                        <div class="tech-workflows">
                            <h4>Available Workflows</h4>
                            ${Object.entries(tech.workflows).map(([workflowId, workflow]) => `
                                <div class="workflow-item">
                                    <div class="workflow-info">
                                        <span class="workflow-name">${this.formatWorkflowName(workflowId)}</span>
                                        <span class="workflow-complexity ${workflow.complexity}">${workflow.complexity}</span>
                                    </div>
                                    <div class="workflow-accuracy">${(workflow.accuracy * 100).toFixed(2)}%</div>
                                    <button class="btn-professional btn-primary-pro execute-workflow-btn" 
                                            data-technology="${techId}" data-workflow="${workflowId}">
                                        Execute
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    updateActiveWorkflowsDisplay() {
        const container = document.getElementById('active-workflows');
        if (!container) return;

        const workflows = Array.from(this.activeWorkflows.values());
        
        container.innerHTML = `
            <div class="active-workflows-header">
                <h3>🔄 Active Workflows</h3>
                <div class="workflows-stats">
                    <span class="stat-item">
                        <span class="stat-value">${workflows.filter(w => w.status === 'running').length}</span>
                        <span class="stat-label">Running</span>
                    </span>
                    <span class="stat-item">
                        <span class="stat-value">${workflows.filter(w => w.status === 'completed').length}</span>
                        <span class="stat-label">Completed</span>
                    </span>
                    <span class="stat-item">
                        <span class="stat-value">${workflows.length}</span>
                        <span class="stat-label">Total</span>
                    </span>
                </div>
            </div>
            
            <div class="workflows-list">
                ${workflows.map(workflow => this.renderWorkflowCard(workflow)).join('')}
            </div>
        `;
    }

    renderWorkflowCard(workflow) {
        const statusColors = {
            'running': '#3B82F6',
            'completed': '#10B981',
            'failed': '#EF4444',
            'pending': '#F59E0B'
        };

        return `
            <div class="workflow-card professional-card" id="workflow-${workflow.id}">
                <div class="workflow-card-header">
                    <div class="workflow-title">
                        <span class="tech-icon">${this.getTechnologyIcon(workflow.technology)}</span>
                        <div>
                            <h4>${this.formatWorkflowName(workflow.workflow_type)}</h4>
                            <span class="workflow-tech">${workflow.technology.replace('_', ' ').toUpperCase()}</span>
                        </div>
                    </div>
                    <div class="workflow-status" style="color: ${statusColors[workflow.status]}">
                        ${workflow.status.toUpperCase()}
                    </div>
                </div>
                
                <div class="workflow-metrics">
                    <div class="metric-item">
                        <span class="metric-label">Progress</span>
                        <div class="progress-bar-pro">
                            <div class="progress-fill-pro" style="width: ${workflow.progress}%"></div>
                        </div>
                        <span class="metric-value">${workflow.progress.toFixed(1)}%</span>
                    </div>
                    
                    <div class="metric-item">
                        <span class="metric-label">Accuracy</span>
                        <span class="metric-value accuracy-value">${(workflow.accuracy * 100).toFixed(2)}%</span>
                    </div>
                    
                    <div class="metric-item">
                        <span class="metric-label">Target</span>
                        <span class="metric-value">${(workflow.accuracy_target * 100).toFixed(2)}%</span>
                    </div>
                </div>
                
                <div class="lab-environment">
                    <h5>🔬 Lab Environment</h5>
                    <div class="lab-details">
                        ${Object.entries(workflow.lab_environment || {}).slice(0, 3).map(([key, value]) => `
                            <span class="lab-item">
                                <strong>${key.replace(/_/g, ' ')}:</strong> ${value}
                            </span>
                        `).join('')}
                    </div>
                </div>
                
                <div class="workflow-actions">
                    <button class="btn-professional btn-secondary-pro" onclick="comprehensiveWorkflows.viewWorkflowDetails('${workflow.id}')">
                        View Details
                    </button>
                    ${workflow.status === 'running' ? 
                        `<button class="btn-professional btn-secondary-pro" onclick="comprehensiveWorkflows.pauseWorkflow('${workflow.id}')">Pause</button>` :
                        ''
                    }
                </div>
            </div>
        `;
    }

    updateNetworkStatus(networkStatus) {
        Object.entries(networkStatus).forEach(([network, status]) => {
            this.networkStatus.set(network, status);
        });
        this.updateNetworkStatusDisplay();
    }

    updateNetworkStatusDisplay() {
        const container = document.getElementById('network-status');
        if (!container) return;

        const networks = Array.from(this.networkStatus.entries());
        
        container.innerHTML = `
            <div class="network-status-header">
                <h3>🌐 Network Status</h3>
                <div class="overall-health ${this.getOverallNetworkHealth()}">
                    ${this.getOverallNetworkHealth().toUpperCase()}
                </div>
            </div>
            
            <div class="networks-grid">
                ${networks.map(([networkId, network]) => `
                    <div class="network-status-card professional-card">
                        <div class="network-header">
                            <span class="network-icon">${this.getNetworkIcon(networkId)}</span>
                            <h4>${networkId.replace('_', ' ').toUpperCase()}</h4>
                            <div class="status-indicator ${network.status}">${network.status}</div>
                        </div>
                        
                        <div class="network-metrics">
                            <div class="metric">
                                <span class="metric-label">Nodes</span>
                                <span class="metric-value">${network.nodes}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Accuracy</span>
                                <span class="metric-value">${(network.accuracy * 100).toFixed(2)}%</span>
                            </div>
                        </div>
                        
                        <div class="accuracy-bar accuracy-${this.getAccuracyLevel(network.accuracy)}">
                            <div class="accuracy-fill" style="width: ${network.accuracy * 100}%"></div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    updateAccuracyMetrics(accuracyMetrics) {
        Object.entries(accuracyMetrics).forEach(([tech, metrics]) => {
            this.accuracyMetrics.set(tech, metrics);
        });
        this.updateAccuracyMetricsDisplay();
    }

    updateAccuracyMetricsDisplay() {
        const container = document.getElementById('accuracy-metrics');
        if (!container) return;

        const metrics = Array.from(this.accuracyMetrics.entries());
        
        container.innerHTML = `
            <div class="accuracy-metrics-header">
                <h3>📊 Accuracy Metrics</h3>
                <div class="overall-accuracy">
                    ${(this.calculateOverallAccuracy() * 100).toFixed(2)}% Overall
                </div>
            </div>
            
            <div class="accuracy-charts">
                ${metrics.map(([techId, metric]) => `
                    <div class="accuracy-chart professional-card">
                        <div class="chart-header">
                            <span class="tech-icon">${this.getTechnologyIcon(techId)}</span>
                            <h4>${techId.replace('_', ' ').toUpperCase()}</h4>
                            <span class="current-accuracy">${(metric.current_accuracy * 100).toFixed(2)}%</span>
                        </div>
                        
                        <div class="accuracy-details">
                            <div class="detail-item">
                                <span class="detail-label">Success Rate</span>
                                <span class="detail-value">${(metric.success_rate * 100).toFixed(1)}%</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Error Rate</span>
                                <span class="detail-value">${(metric.error_rate * 100).toFixed(2)}%</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Performance</span>
                                <span class="detail-value">${(metric.performance_score * 100).toFixed(1)}%</span>
                            </div>
                        </div>
                        
                        <div class="accuracy-trend">
                            <div class="trend-line">
                                ${metric.historical_accuracy ? this.renderAccuracyTrend(metric.historical_accuracy) : ''}
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    getTechnologyIcon(techId) {
        const icons = {
            'web3_blockchain': '🔗',
            'quantum_computing': '⚛️',
            'ai_neural_networks': '🧠',
            'data_science_analytics': '📊',
            'iot_systems': '📡',
            'genomics_bioinformatics': '🧬'
        };
        return icons[techId] || '⚙️';
    }

    getNetworkIcon(networkId) {
        const icons = {
            'quantum_network': '⚛️',
            'ai_cluster': '🧠',
            'blockchain_network': '🔗',
            'iot_mesh': '📡',
            'genomics_cluster': '🧬'
        };
        return icons[networkId] || '🌐';
    }

    formatWorkflowName(workflowId) {
        return workflowId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    getAccuracyLevel(accuracy) {
        if (accuracy >= 0.995) return 'excellent';
        if (accuracy >= 0.99) return 'good';
        if (accuracy >= 0.985) return 'fair';
        return 'poor';
    }

    getOverallNetworkHealth() {
        const networks = Array.from(this.networkStatus.values());
        const avgAccuracy = networks.reduce((sum, net) => sum + net.accuracy, 0) / networks.length;
        return this.getAccuracyLevel(avgAccuracy);
    }

    calculateOverallAccuracy() {
        const metrics = Array.from(this.accuracyMetrics.values());
        if (metrics.length === 0) return 0.99;
        return metrics.reduce((sum, metric) => sum + metric.current_accuracy, 0) / metrics.length;
    }

    setupEventListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('execute-workflow-btn')) {
                const technology = e.target.dataset.technology;
                const workflow = e.target.dataset.workflow;
                this.showWorkflowParametersModal(technology, workflow);
            }
        });
    }

    showWorkflowParametersModal(technology, workflow) {
        // Create and show modal for workflow parameters
        const modal = document.createElement('div');
        modal.className = 'workflow-modal';
        modal.innerHTML = `
            <div class="modal-content professional-card">
                <div class="modal-header">
                    <h3>Execute ${this.formatWorkflowName(workflow)}</h3>
                    <button class="modal-close" onclick="this.closest('.workflow-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <p>Technology: <strong>${technology.replace('_', ' ').toUpperCase()}</strong></p>
                    <p>Workflow: <strong>${this.formatWorkflowName(workflow)}</strong></p>
                    <div class="parameters-section">
                        <h4>Parameters (Optional)</h4>
                        <textarea id="workflow-parameters" placeholder="Enter JSON parameters..." rows="4"></textarea>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-professional btn-secondary-pro" onclick="this.closest('.workflow-modal').remove()">
                        Cancel
                    </button>
                    <button class="btn-professional btn-primary-pro" onclick="comprehensiveWorkflows.executeWorkflowFromModal('${technology}', '${workflow}')">
                        Execute Workflow
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    executeWorkflowFromModal(technology, workflow) {
        const parametersText = document.getElementById('workflow-parameters').value;
        let parameters = {};
        
        if (parametersText.trim()) {
            try {
                parameters = JSON.parse(parametersText);
            } catch (error) {
                this.showNotification('Invalid JSON parameters', 'error');
                return;
            }
        }
        
        this.executeWorkflow(technology, workflow, parameters);
        document.querySelector('.workflow-modal').remove();
    }

    loadWorkflowDashboard() {
        const dashboard = document.getElementById('comprehensive-workflows-dashboard');
        if (!dashboard) return;

        dashboard.innerHTML = `
            <div class="comprehensive-dashboard-container">
                <div class="dashboard-header">
                    <h2>🔬 Comprehensive Technology Workflows</h2>
                    <p>Advanced workflows for Web3, AI, Quantum Computing, IoT, Data Science, and Genomics</p>
                </div>
                
                <div class="dashboard-tabs">
                    <button class="tab-btn active" data-tab="overview">Overview</button>
                    <button class="tab-btn" data-tab="workflows">Active Workflows</button>
                    <button class="tab-btn" data-tab="networks">Network Status</button>
                    <button class="tab-btn" data-tab="accuracy">Accuracy Metrics</button>
                    <button class="tab-btn" data-tab="lab">Lab Environments</button>
                </div>
                
                <div class="tab-content">
                    <div id="overview-tab" class="tab-panel active">
                        <div id="technologies-overview"></div>
                    </div>
                    
                    <div id="workflows-tab" class="tab-panel">
                        <div id="active-workflows"></div>
                    </div>
                    
                    <div id="networks-tab" class="tab-panel">
                        <div id="network-status"></div>
                    </div>
                    
                    <div id="accuracy-tab" class="tab-panel">
                        <div id="accuracy-metrics"></div>
                    </div>
                    
                    <div id="lab-tab" class="tab-panel">
                        <div id="lab-environments"></div>
                    </div>
                </div>
            </div>
        `;

        this.setupTabNavigation();
        this.updateTechnologiesDisplay();
    }

    setupTabNavigation() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.target.dataset.tab;
                
                // Update active tab
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-panel').forEach(p => p.classList.remove('active'));
                
                e.target.classList.add('active');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
    }

    startRealTimeMonitoring() {
        setInterval(() => {
            this.loadAccuracyMetrics();
            this.loadNetworkStatus();
        }, 10000);
    }

    async loadAccuracyMetrics() {
        try {
            const response = await fetch('http://localhost:8008/api/accuracy/metrics');
            if (response.ok) {
                const data = await response.json();
                this.updateAccuracyMetrics(data.metrics);
            }
        } catch (error) {
            console.log('Failed to load accuracy metrics');
        }
    }

    async loadNetworkStatus() {
        try {
            const response = await fetch('http://localhost:8008/api/network/status');
            if (response.ok) {
                const data = await response.json();
                this.updateNetworkStatus(data.network_status);
            }
        } catch (error) {
            console.log('Failed to load network status');
        }
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('workflows-connection-status');
        if (statusElement) {
            const statusConfig = {
                'connected': { text: 'Connected', class: 'status-connected', icon: '🟢' },
                'disconnected': { text: 'Disconnected', class: 'status-disconnected', icon: '🔴' },
                'fallback': { text: 'Offline Mode', class: 'status-fallback', icon: '🟡' }
            };
            
            const config = statusConfig[status] || statusConfig['fallback'];
            statusElement.innerHTML = `${config.icon} ${config.text}`;
            statusElement.className = `connection-status ${config.class}`;
        }
    }

    showNotification(message, type = 'info') {
        if (window.workflowManager && window.workflowManager.showNotification) {
            window.workflowManager.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Global comprehensive workflows manager
let comprehensiveWorkflows;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('comprehensive-workflows-dashboard')) {
        comprehensiveWorkflows = new ComprehensiveWorkflowsManager();
        window.comprehensiveWorkflows = comprehensiveWorkflows;
    }
});

// Global functions
function executeWorkflow(technology, workflow, parameters) {
    if (comprehensiveWorkflows) {
        comprehensiveWorkflows.executeWorkflow(technology, workflow, parameters);
    }
}

function viewWorkflowDetails(workflowId) {
    if (comprehensiveWorkflows) {
        comprehensiveWorkflows.viewWorkflowDetails(workflowId);
    }
}
