{"name": "kontour-cybersecurity-design-service", "version": "1.0.0", "description": "Cybersecurity and Design Thinking Service with AI Ethics and Innovation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["cybersecurity", "design-thinking", "ai-ethics", "threat-detection", "quantum-cryptography", "blockchain-security", "user-testing", "innovation", "kontour"], "author": "Kontour Coin Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "node-fetch": "^3.3.2", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}