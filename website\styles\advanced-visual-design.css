/* Advanced Visual Design System for Kontour Coin Web3 Integration */

/* Advanced Color System with Accuracy Indicators */
:root {
    /* Web3 Specific Colors */
    --web3-primary: linear-gradient(135deg, #1E40AF 0%, #3B82F6 50%, #60A5FA 100%);
    --web3-secondary: linear-gradient(135deg, #059669 0%, #10B981 50%, #34D399 100%);
    --web3-warning: linear-gradient(135deg, #D97706 0%, #F59E0B 50%, #FBBF24 100%);
    --web3-error: linear-gradient(135deg, #DC2626 0%, #EF4444 50%, #F87171 100%);
    
    /* Accuracy Color Scale */
    --accuracy-excellent: #10B981; /* 95%+ */
    --accuracy-good: #3B82F6;      /* 90-95% */
    --accuracy-fair: #F59E0B;      /* 85-90% */
    --accuracy-poor: #EF4444;      /* <85% */
    
    /* Network Colors */
    --ethereum-color: #627EEA;
    --polygon-color: #8247E5;
    --bsc-color: #F3BA2F;
    --avalanche-color: #E84142;
    --arbitrum-color: #28A0F0;
    --optimism-color: #FF0420;
    
    /* Advanced Gradients */
    --neural-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --quantum-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --blockchain-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --ai-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    
    /* 3D Effects */
    --shadow-3d: 0 10px 30px rgba(0, 0, 0, 0.3), 0 1px 8px rgba(0, 0, 0, 0.2);
    --shadow-floating: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    
    /* Advanced Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Advanced Web3 Dashboard Styling */
.web3-dashboard-container {
    background: var(--surface-primary);
    border-radius: 24px;
    padding: 2rem;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.web3-dashboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--web3-primary);
    opacity: 0.8;
}

/* Professional Wallet Connection Interface */
.web3-connection-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.wallet-connect-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.connect-wallet-btn {
    position: relative;
    overflow: hidden;
    transition: var(--transition-smooth);
    transform-style: preserve-3d;
}

.connect-wallet-btn:hover {
    transform: translateY(-3px) rotateX(5deg);
    box-shadow: var(--shadow-floating);
}

.connect-wallet-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.connect-wallet-btn:hover::after {
    left: 100%;
}

/* Advanced Wallet Info Display */
.wallet-connection-info {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 1rem 0;
    position: relative;
}

.wallet-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.wallet-avatar {
    width: 60px;
    height: 60px;
    background: var(--web3-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-3d);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.wallet-details h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.wallet-address {
    font-family: 'JetBrains Mono', monospace;
    color: var(--text-secondary);
    font-size: 0.9rem;
    background: var(--surface-tertiary);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    display: inline-block;
    margin: 0.25rem 0;
}

.wallet-network {
    color: var(--text-muted);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.wallet-accuracy {
    text-align: center;
    margin-left: auto;
}

.accuracy-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.accuracy-label {
    font-size: 0.7rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Advanced Metrics Visualization */
.web3-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.metric-card-pro {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: var(--transition-smooth);
}

.metric-card-pro::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.metric-card-pro:hover::before {
    transform: scaleX(1);
}

.metric-card-pro:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-floating);
}

/* Advanced Transaction Status */
.transaction-status-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.transaction-status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 12px;
    transition: var(--transition-smooth);
}

.transaction-status-item.pending {
    background: rgba(251, 191, 36, 0.1);
    border-left: 4px solid var(--accuracy-fair);
}

.transaction-status-item.success {
    background: rgba(16, 185, 129, 0.1);
    border-left: 4px solid var(--accuracy-excellent);
}

.transaction-status-item.error {
    background: rgba(239, 68, 68, 0.1);
    border-left: 4px solid var(--accuracy-poor);
}

.status-icon {
    font-size: 1.2rem;
    animation: pulse 2s infinite;
}

/* Advanced Transaction History */
.transaction-history-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.transaction-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.transaction-count {
    background: var(--primary-gradient);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.transaction-list {
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--glass-border) transparent;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: var(--surface-secondary);
    transition: var(--transition-smooth);
    position: relative;
}

.transaction-item:hover {
    background: var(--surface-tertiary);
    transform: translateX(5px);
}

.transaction-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.transaction-item:hover::before {
    opacity: 1;
}

.transaction-type {
    font-weight: 600;
    color: var(--text-primary);
    text-transform: capitalize;
}

.transaction-hash {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.transaction-accuracy {
    font-size: 0.75rem;
    color: var(--accuracy-excellent);
    font-weight: 500;
}

.transaction-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.transaction-status.success {
    background: rgba(16, 185, 129, 0.2);
    color: var(--accuracy-excellent);
}

.transaction-status.pending {
    background: rgba(251, 191, 36, 0.2);
    color: var(--accuracy-fair);
}

.transaction-status.error {
    background: rgba(239, 68, 68, 0.2);
    color: var(--accuracy-poor);
}

/* Advanced Network Indicators */
.network-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.network-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    z-index: -1;
}

.network-ethereum::before { background: var(--ethereum-color); }
.network-polygon::before { background: var(--polygon-color); }
.network-bsc::before { background: var(--bsc-color); }
.network-avalanche::before { background: var(--avalanche-color); }
.network-arbitrum::before { background: var(--arbitrum-color); }
.network-optimism::before { background: var(--optimism-color); }

/* Advanced Loading States */
.web3-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.web3-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--glass-border);
    border-top: 3px solid var(--primary-gradient);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Advanced Accuracy Visualization */
.accuracy-bar {
    width: 100%;
    height: 8px;
    background: var(--surface-tertiary);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.accuracy-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.accuracy-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: accuracy-shine 2s infinite;
}

@keyframes accuracy-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.accuracy-excellent .accuracy-fill { background: var(--accuracy-excellent); }
.accuracy-good .accuracy-fill { background: var(--accuracy-good); }
.accuracy-fair .accuracy-fill { background: var(--accuracy-fair); }
.accuracy-poor .accuracy-fill { background: var(--accuracy-poor); }

/* Advanced Responsive Design */
@media (max-width: 768px) {
    .web3-dashboard-container {
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .wallet-connect-buttons {
        grid-template-columns: 1fr;
    }
    
    .wallet-header {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .web3-metrics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Advanced Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Advanced Network and Contract Cards */
.networks-grid, .contracts-grid, .defi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.network-card, .contract-card, .defi-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 2rem;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.network-card::before, .contract-card::before, .defi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.network-card:hover::before, .contract-card:hover::before, .defi-card:hover::before {
    transform: scaleX(1);
}

.network-card:hover, .contract-card:hover, .defi-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-floating);
    border-color: rgba(255, 255, 255, 0.3);
}

.network-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    text-align: center;
    animation: float 3s ease-in-out infinite;
}

.network-accuracy, .contract-accuracy, .defi-accuracy {
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 0.9rem;
    margin: 0.5rem 0;
}

.network-features, .contract-functions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.feature-tag {
    background: var(--surface-tertiary);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.contract-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.contract-status.verified {
    background: rgba(16, 185, 129, 0.2);
    color: var(--accuracy-excellent);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.contract-address {
    font-family: 'JetBrains Mono', monospace;
    background: var(--surface-secondary);
    padding: 0.5rem;
    border-radius: 8px;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0.5rem 0;
}

.defi-icon {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    display: block;
    animation: pulse 2s infinite;
}

/* Advanced 3D Button Effects */
.btn-professional {
    position: relative;
    transform-style: preserve-3d;
    transition: var(--transition-smooth);
}

.btn-professional:hover {
    transform: translateY(-3px) rotateX(10deg);
}

.btn-professional:active {
    transform: translateY(-1px) rotateX(5deg);
}

.btn-professional::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.btn-professional:hover::after {
    transform: translateX(100%);
}

/* Advanced Accuracy Indicators */
.accuracy-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    position: relative;
}

.accuracy-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.accuracy-excellent {
    background: rgba(16, 185, 129, 0.2);
    color: var(--accuracy-excellent);
}

.accuracy-excellent::before {
    background: var(--accuracy-excellent);
}

.accuracy-good {
    background: rgba(59, 130, 246, 0.2);
    color: var(--accuracy-good);
}

.accuracy-good::before {
    background: var(--accuracy-good);
}

/* Advanced Connection Status */
.connection-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    transition: var(--transition-smooth);
}

.status-connected {
    background: rgba(16, 185, 129, 0.2);
    color: var(--accuracy-excellent);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-connecting {
    background: rgba(251, 191, 36, 0.2);
    color: var(--accuracy-fair);
    border: 1px solid rgba(251, 191, 36, 0.3);
    animation: pulse 1.5s infinite;
}

.status-error {
    background: rgba(239, 68, 68, 0.2);
    color: var(--accuracy-poor);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-none {
    background: rgba(156, 163, 175, 0.2);
    color: var(--text-muted);
    border: 1px solid rgba(156, 163, 175, 0.3);
}

/* Advanced Wallet Balance Display */
.wallet-balance {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--surface-secondary);
    padding: 1rem;
    border-radius: 12px;
    margin-top: 1rem;
}

.balance-item {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
}

.balance-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.balance-symbol {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
}

.balance-usd {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Advanced Scrollbar Styling */
.transaction-list::-webkit-scrollbar {
    width: 6px;
}

.transaction-list::-webkit-scrollbar-track {
    background: var(--surface-tertiary);
    border-radius: 3px;
}

.transaction-list::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 3px;
}

.transaction-list::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}

/* Advanced Hover Effects */
.professional-card:hover {
    background: var(--surface-secondary);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-4px);
    box-shadow: var(--shadow-floating);
}

.professional-card:hover .network-icon,
.professional-card:hover .defi-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Advanced Loading Animation */
.web3-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.web3-loading-content {
    text-align: center;
    color: var(--text-primary);
}

.web3-loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--glass-border);
    border-top: 4px solid var(--primary-gradient);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(0, 0, 0, 0.9);
        --glass-border: rgba(255, 255, 255, 0.8);
        --text-primary: #FFFFFF;
        --text-secondary: #E0E0E0;
    }
}
