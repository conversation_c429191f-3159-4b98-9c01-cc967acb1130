/* Kontour Coin - Advanced Professional Design System */

/* ===== CSS VARIABLES ===== */
:root {
    /* Primary Brand Colors */
    --primary-purple: #8B5CF6;
    --primary-purple-dark: #7C3AED;
    --primary-purple-light: #A78BFA;
    --secondary-purple: #C084FC;
    --accent-purple: #DDD6FE;
    
    /* Gradient Definitions */
    --gradient-primary: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%);
    --gradient-secondary: linear-gradient(135deg, #C084FC 0%, #A78BFA 50%, #8B5CF6 100%);
    --gradient-accent: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
    --gradient-dark: linear-gradient(135deg, #1F2937 0%, #111827 100%);
    
    /* Neutral Colors */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    
    /* Status Colors */
    --success: #10B981;
    --success-light: #D1FAE5;
    --warning: #F59E0B;
    --warning-light: #FEF3C7;
    --error: #EF4444;
    --error-light: #FEE2E2;
    --info: #3B82F6;
    --info-light: #DBEAFE;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    
    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    
    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-purple: 0 10px 25px -5px rgba(139, 92, 246, 0.3);
    --shadow-purple-lg: 0 20px 40px -10px rgba(139, 92, 246, 0.4);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-weight: var(--font-normal);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-bold);
    line-height: 1.2;
    margin-bottom: var(--space-4);
    color: var(--gray-900);
}

h1 {
    font-size: var(--text-5xl);
    font-weight: var(--font-extrabold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    font-size: var(--text-4xl);
    font-weight: var(--font-bold);
}

h3 {
    font-size: var(--text-3xl);
    font-weight: var(--font-semibold);
}

h4 {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
}

h5 {
    font-size: var(--text-xl);
    font-weight: var(--font-medium);
}

h6 {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
}

p {
    margin-bottom: var(--space-4);
    color: var(--gray-600);
    line-height: 1.7;
}

a {
    color: var(--primary-purple);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-purple-dark);
    text-decoration: underline;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--space-6);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-primary { color: var(--primary-purple); }
.text-secondary { color: var(--secondary-purple); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-gray { color: var(--gray-500); }

.bg-primary { background-color: var(--primary-purple); }
.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-secondary { background: var(--gradient-secondary); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-purple { box-shadow: var(--shadow-purple); }

/* ===== GRID SYSTEM ===== */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.space-x-4 > * + * {
    margin-left: var(--space-4);
}

.space-y-4 > * + * {
    margin-top: var(--space-4);
}

.space-y-8 > * + * {
    margin-top: var(--space-8);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-4);
    }
    
    h1 {
        font-size: var(--text-4xl);
    }
    
    h2 {
        font-size: var(--text-3xl);
    }
    
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: var(--text-3xl);
    }

    h2 {
        font-size: var(--text-2xl);
    }
}

/* ===== COMPONENT STYLES ===== */

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--gray-900);
    text-decoration: none;
}

.navbar-brand:hover {
    text-decoration: none;
}

.navbar-logo {
    width: 40px;
    height: 40px;
    margin-right: var(--space-3);
}

.navbar-nav {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.navbar-nav li {
    margin: 0 var(--space-4);
}

.navbar-nav a {
    color: var(--gray-700);
    font-weight: var(--font-medium);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
}

.navbar-nav a:hover {
    color: var(--primary-purple);
    background-color: var(--accent-purple);
    text-decoration: none;
}

.navbar-nav a.active {
    color: var(--primary-purple);
    background-color: var(--accent-purple);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    line-height: 1;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    text-decoration: none;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: 2px solid var(--primary-purple);
    outline-offset: 2px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-purple);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-purple-lg);
    color: var(--white);
    text-decoration: none;
}

.btn-secondary {
    background: var(--white);
    color: var(--primary-purple);
    border: 2px solid var(--primary-purple);
}

.btn-secondary:hover {
    background: var(--primary-purple);
    color: var(--white);
    text-decoration: none;
}

.btn-outline {
    background: transparent;
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
}

.btn-outline:hover {
    background: var(--gray-100);
    border-color: var(--gray-400);
    text-decoration: none;
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.card-gradient {
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
}

.card-gradient .card-header,
.card-gradient .card-footer {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e5e7eb" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
    z-index: -1;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    z-index: 1;
}

.hero-title {
    font-size: var(--text-6xl);
    font-weight: var(--font-extrabold);
    margin-bottom: var(--space-6);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* Feature Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    margin: var(--space-16) 0;
}

.feature-card {
    text-align: center;
    padding: var(--space-8);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    background: var(--gradient-primary);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-3xl);
    color: var(--white);
}

.feature-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-4);
    color: var(--gray-900);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.7;
}

/* Stats Section */
.stats-section {
    background: var(--gradient-primary);
    color: var(--white);
    padding: var(--space-20) 0;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.stat-item {
    padding: var(--space-6);
}

.stat-number {
    font-size: var(--text-5xl);
    font-weight: var(--font-extrabold);
    margin-bottom: var(--space-2);
    display: block;
}

.stat-label {
    font-size: var(--text-lg);
    opacity: 0.9;
}

/* Testimonials */
.testimonials-section {
    padding: var(--space-20) 0;
    background: var(--gray-50);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.testimonial-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    position: relative;
}

.testimonial-quote {
    font-size: var(--text-lg);
    line-height: 1.7;
    margin-bottom: var(--space-6);
    color: var(--gray-700);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: var(--font-bold);
    font-size: var(--text-xl);
}

.testimonial-info h4 {
    margin: 0;
    font-size: var(--text-lg);
    color: var(--gray-900);
}

.testimonial-info p {
    margin: 0;
    color: var(--gray-500);
    font-size: var(--text-sm);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: var(--space-20) 0 var(--space-8);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-12);
}

.footer-section h3 {
    color: var(--white);
    margin-bottom: var(--space-6);
    font-size: var(--text-xl);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: var(--space-3);
}

.footer-section ul li a {
    color: var(--gray-400);
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--primary-purple-light);
    text-decoration: none;
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: var(--space-8);
    text-align: center;
    color: var(--gray-500);
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary-purple);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Navigation */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--text-2xl);
    color: var(--gray-700);
    cursor: pointer;
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--white);
    z-index: var(--z-modal);
    padding: var(--space-8);
}

.mobile-menu.active {
    display: block;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: var(--text-2xl);
    color: var(--gray-700);
    cursor: pointer;
}

.mobile-menu-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-menu-nav li {
    margin-bottom: var(--space-4);
}

.mobile-menu-nav a {
    display: block;
    padding: var(--space-4);
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    color: var(--gray-700);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
}

.mobile-menu-nav a:hover {
    background: var(--accent-purple);
    color: var(--primary-purple);
    text-decoration: none;
}

@media (max-width: 768px) {
    .navbar-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-title {
        font-size: var(--text-4xl);
    }

    .hero-subtitle {
        font-size: var(--text-lg);
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }
}
