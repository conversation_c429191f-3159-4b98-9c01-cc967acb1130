@echo off
echo 🚀 DEPLOYING KONTOUR COIN COMPLETE AI PLATFORM
echo ================================================

echo.
echo 📦 Installing dependencies for all services...
echo.

echo Installing LLM Service dependencies...
cd services\llm-service
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install LLM service dependencies
    pause
    exit /b 1
)
cd ..\..

echo Installing Exchange Integration dependencies...
cd services\exchange-integration
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install Exchange Integration dependencies
    pause
    exit /b 1
)
cd ..\..

echo Installing Workflow Orchestrator dependencies...
cd services\workflow-orchestrator
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install Workflow Orchestrator dependencies
    pause
    exit /b 1
)
cd ..\..

echo.
echo ✅ All dependencies installed successfully!
echo.

echo 🚀 Starting all services...
echo.

echo Starting LLM Service on port 8001...
start "LLM Service" cmd /k "cd services\llm-service && npm start"
timeout /t 3 /nobreak >nul

echo Starting Exchange Integration Service on port 8002...
start "Exchange Integration" cmd /k "cd services\exchange-integration && npm start"
timeout /t 3 /nobreak >nul

echo Starting Workflow Orchestrator on port 8000...
start "Workflow Orchestrator" cmd /k "cd services\workflow-orchestrator && npm start"
timeout /t 3 /nobreak >nul

echo.
echo 🌐 Starting website server...
cd website
start "Kontour Website" cmd /k "python -m http.server 3000"
cd ..

echo.
echo ================================================
echo 🎉 KONTOUR COIN PLATFORM DEPLOYED SUCCESSFULLY!
echo ================================================
echo.
echo 🤖 LLM Service:              http://localhost:8001
echo 🏦 Exchange Integration:     http://localhost:8002  
echo 🔄 Workflow Orchestrator:    http://localhost:8000
echo 💎 Kontour Website:          http://localhost:3000
echo.
echo 📊 Service Health Checks:
echo    LLM Health:               http://localhost:8001/health
echo    Exchange Health:          http://localhost:8002/health
echo    Workflow Health:          http://localhost:8000/health
echo.
echo 🚀 FEATURES AVAILABLE:
echo    ✅ AI Agents Management
echo    ✅ LLM Chat Integration
echo    ✅ Multi-Exchange Trading
echo    ✅ Arbitrage Detection
echo    ✅ Workflow Automation
echo    ✅ Real-time Market Data
echo    ✅ Agent Coordination
echo    ✅ Performance Analytics
echo.
echo Press any key to open the website...
pause >nul
start http://localhost:3000

echo.
echo 💡 To stop all services, close the terminal windows or press Ctrl+C in each
echo.
pause
