// Professional AI Assistant with Real-time Features for Kontour Coin
class ProfessionalAIAssistant {
    constructor() {
        this.conversations = new Map();
        this.activeAssistantId = null;
        this.isTyping = false;
        this.websocket = null;
        this.proactiveEnabled = true;
        this.assistantMetrics = new Map();
        this.realTimeFeatures = {
            market_monitoring: true,
            proactive_alerts: true,
            continuous_learning: true,
            multi_modal_support: true
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeChatInterface();
        this.connectWebSocket();
        this.createProfessionalAssistant();
    }

    connectWebSocket() {
        try {
            this.websocket = new WebSocket('ws://localhost:8001');

            this.websocket.onopen = () => {
                console.log('Professional AI Assistant connected');
                this.updateConnectionStatus('connected');
            };

            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };

            this.websocket.onclose = () => {
                console.log('Professional AI Assistant disconnected');
                this.updateConnectionStatus('disconnected');
                // Attempt reconnection after 5 seconds
                setTimeout(() => this.connectWebSocket(), 5000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus('error');
            };
        } catch (error) {
            console.log('WebSocket connection failed, using fallback mode');
            this.updateConnectionStatus('fallback');
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'assistant_created':
                this.activeAssistantId = data.payload.id;
                this.assistantMetrics.set(this.activeAssistantId, data.payload.performance_metrics);
                this.updateAssistantInfo(data.payload);
                break;

            case 'llm_response':
                this.hideTypingIndicator();
                this.addMessageToChat(data.payload.response, 'ai');
                this.updateAssistantMetrics(data.payload.metadata);
                break;

            case 'proactive_insight':
                if (this.proactiveEnabled) {
                    this.displayProactiveInsight(data);
                }
                break;

            case 'analytics_data':
                this.updateRealTimeAnalytics(data.payload);
                break;

            case 'error':
                this.hideTypingIndicator();
                this.addMessageToChat(`❌ Error: ${data.payload.error}`, 'ai');
                break;
        }
    }

    async createProfessionalAssistant() {
        if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
            // Fallback to creating a mock assistant
            this.activeAssistantId = 'fallback_assistant';
            return;
        }

        const assistantConfig = {
            name: 'Kontour AI Professional Assistant',
            type: 'professional',
            capabilities: [
                'market_analysis',
                'trading_strategies',
                'portfolio_optimization',
                'risk_assessment',
                'quantum_computing',
                'genomic_analysis',
                'cybersecurity',
                'design_thinking'
            ],
            real_time_features: this.realTimeFeatures
        };

        this.websocket.send(JSON.stringify({
            type: 'create_assistant',
            payload: {
                userId: 'user_' + Date.now(),
                config: assistantConfig
            }
        }));

        // Subscribe to proactive insights
        setTimeout(() => {
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({
                    type: 'subscribe_proactive',
                    payload: {}
                }));
            }
        }, 1000);
    }

    displayProactiveInsight(data) {
        const proactiveDiv = document.createElement('div');
        proactiveDiv.className = 'message ai-message proactive-insight';

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        proactiveDiv.innerHTML = `
            <div class="message-content">
                <div class="proactive-header">
                    <span class="proactive-icon">🚨</span>
                    <span class="proactive-label">Proactive AI Insight</span>
                    <span class="proactive-time">${timestamp}</span>
                </div>
                <div class="proactive-message">
                    ${this.formatMessage(data.message)}
                </div>
                <div class="proactive-actions">
                    <button class="btn-mini btn-primary" onclick="professionalAI.respondToInsight('${data.analysis?.trend || 'general'}')">
                        Get Detailed Analysis
                    </button>
                    <button class="btn-mini btn-secondary" onclick="professionalAI.dismissInsight(this)">
                        Dismiss
                    </button>
                </div>
            </div>
        `;

        const chatMessages = document.getElementById('chat-messages');
        chatMessages.appendChild(proactiveDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Add notification sound/animation
        this.playNotificationSound();
        this.animateProactiveInsight(proactiveDiv);
    }

    respondToInsight(trend) {
        const responses = {
            'bullish': 'Please provide detailed analysis of the bullish momentum and specific trading recommendations.',
            'bearish': 'I need a comprehensive risk assessment and defensive strategy recommendations.',
            'general': 'Can you elaborate on this insight with specific actionable recommendations?'
        };

        const input = document.getElementById('chat-input');
        input.value = responses[trend] || responses['general'];
        this.sendMessage();
    }

    dismissInsight(element) {
        element.closest('.proactive-insight').style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => {
            element.closest('.proactive-insight').remove();
        }, 300);
    }

    updateAssistantInfo(assistant) {
        const infoContainer = document.getElementById('chat-agent-info');
        if (infoContainer) {
            infoContainer.innerHTML = `
                <div class="assistant-avatar">🤖</div>
                <div class="assistant-details">
                    <h4>${assistant.name}</h4>
                    <div class="assistant-capabilities">
                        ${assistant.capabilities.slice(0, 3).map(cap =>
                            `<span class="capability-tag">${cap.replace('_', ' ')}</span>`
                        ).join('')}
                    </div>
                    <div class="assistant-status">
                        <span class="status-indicator active"></span>
                        <span>Online & Monitoring</span>
                    </div>
                </div>
            `;
        }
    }

    updateAssistantMetrics(metadata) {
        if (!this.activeAssistantId) return;

        const metrics = this.assistantMetrics.get(this.activeAssistantId) || {};

        if (metadata) {
            metrics.last_response_time = metadata.processing_time || 0;
            metrics.confidence = metadata.confidence || 0;
            metrics.tokens_used = metadata.tokens_used || 0;
        }

        this.assistantMetrics.set(this.activeAssistantId, metrics);
        this.updateMetricsDisplay();
    }

    updateMetricsDisplay() {
        const metricsContainer = document.getElementById('assistant-metrics');
        if (!metricsContainer || !this.activeAssistantId) return;

        const metrics = this.assistantMetrics.get(this.activeAssistantId) || {};

        metricsContainer.innerHTML = `
            <div class="metrics-grid">
                <div class="metric-item">
                    <span class="metric-value">${(metrics.confidence * 100 || 95).toFixed(1)}%</span>
                    <span class="metric-label">Confidence</span>
                </div>
                <div class="metric-item">
                    <span class="metric-value">${metrics.last_response_time || 850}ms</span>
                    <span class="metric-label">Response Time</span>
                </div>
                <div class="metric-item">
                    <span class="metric-value">${metrics.total_interactions || 0}</span>
                    <span class="metric-label">Interactions</span>
                </div>
                <div class="metric-item">
                    <span class="metric-value">${(metrics.user_satisfaction * 100 || 95).toFixed(1)}%</span>
                    <span class="metric-label">Satisfaction</span>
                </div>
            </div>
        `;
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            const statusConfig = {
                'connected': { text: 'Connected', class: 'status-connected', icon: '🟢' },
                'disconnected': { text: 'Disconnected', class: 'status-disconnected', icon: '🔴' },
                'error': { text: 'Connection Error', class: 'status-error', icon: '⚠️' },
                'fallback': { text: 'Offline Mode', class: 'status-fallback', icon: '🟡' }
            };

            const config = statusConfig[status] || statusConfig['fallback'];
            statusElement.innerHTML = `${config.icon} ${config.text}`;
            statusElement.className = `connection-status ${config.class}`;
        }
    }

    playNotificationSound() {
        // Create a subtle notification sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    }

    animateProactiveInsight(element) {
        element.style.animation = 'slideInRight 0.5s ease, pulse 2s infinite 0.5s';
    }

    setupEventListeners() {
        // Chat input handling
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        // Send button
        const sendButton = document.getElementById('send-message-btn');
        if (sendButton) {
            sendButton.addEventListener('click', () => this.sendMessage());
        }

        // Agent selection
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('agent-chat-btn')) {
                const agentId = e.target.dataset.agentId;
                this.openAgentChat(agentId);
            }
        });
    }

    initializeChatInterface() {
        // Create chat interface if it doesn't exist
        if (!document.getElementById('llm-chat-container')) {
            this.createChatInterface();
        }
    }

    createChatInterface() {
        const chatHTML = `
            <div id="llm-chat-container" class="chat-container hidden">
                <div class="chat-header">
                    <div class="chat-agent-info">
                        <div class="agent-avatar">🤖</div>
                        <div class="agent-details">
                            <h4 id="chat-agent-name">AI Agent</h4>
                            <span id="chat-agent-status">Online</span>
                        </div>
                    </div>
                    <div class="chat-controls">
                        <button class="btn btn-sm btn-outline" onclick="llmChat.minimizeChat()">−</button>
                        <button class="btn btn-sm btn-outline" onclick="llmChat.closeChat()">×</button>
                    </div>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <div class="welcome-message">
                        <div class="message ai-message">
                            <div class="message-content">
                                <p>👋 Hello! I'm your AI trading assistant. I can help you with:</p>
                                <ul>
                                    <li>📊 Market analysis and insights</li>
                                    <li>💰 Trading recommendations</li>
                                    <li>📈 Portfolio optimization</li>
                                    <li>⚠️ Risk assessment</li>
                                    <li>🔍 Arbitrage opportunities</li>
                                </ul>
                                <p>What would you like to know?</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="chat-input-container">
                    <div class="chat-suggestions" id="chat-suggestions">
                        <button class="suggestion-btn" onclick="llmChat.sendSuggestion('Analyze BTC market conditions')">
                            📊 Analyze BTC
                        </button>
                        <button class="suggestion-btn" onclick="llmChat.sendSuggestion('Show arbitrage opportunities')">
                            🔍 Arbitrage
                        </button>
                        <button class="suggestion-btn" onclick="llmChat.sendSuggestion('Optimize my portfolio')">
                            📈 Optimize
                        </button>
                    </div>
                    
                    <div class="chat-input-wrapper">
                        <textarea 
                            id="chat-input" 
                            placeholder="Ask me anything about trading, markets, or crypto..."
                            rows="1"
                        ></textarea>
                        <button id="send-message-btn" class="send-btn">
                            <span class="send-icon">➤</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', chatHTML);
    }

    async openAgentChat(agentId) {
        try {
            this.activeAgentId = agentId;
            
            // Get agent details
            const agent = window.workflowManager?.aiAgents.get(agentId);
            if (agent) {
                document.getElementById('chat-agent-name').textContent = agent.name;
                document.getElementById('chat-agent-status').textContent = agent.status;
            }

            // Load conversation history
            await this.loadConversationHistory(agentId);
            
            // Show chat interface
            const chatContainer = document.getElementById('llm-chat-container');
            chatContainer.classList.remove('hidden');
            
            // Focus input
            document.getElementById('chat-input').focus();
            
        } catch (error) {
            console.error('Error opening agent chat:', error);
            this.showError('Failed to open chat with agent');
        }
    }

    async loadConversationHistory(agentId) {
        try {
            const response = await fetch(`http://localhost:8001/api/llm/conversations/${agentId}`);
            if (response.ok) {
                const data = await response.json();
                this.displayConversationHistory(data.messages || []);
            }
        } catch (error) {
            console.log('No previous conversation history found');
        }
    }

    displayConversationHistory(messages) {
        const chatMessages = document.getElementById('chat-messages');
        const welcomeMessage = chatMessages.querySelector('.welcome-message');
        
        if (messages.length > 0) {
            // Hide welcome message if there's history
            if (welcomeMessage) welcomeMessage.style.display = 'none';
            
            // Display last 10 messages
            const recentMessages = messages.slice(-10);
            recentMessages.forEach(msg => {
                this.addMessageToChat(msg.user_message, 'user', false);
                this.addMessageToChat(msg.ai_response, 'ai', false);
            });
        }
    }

    async sendMessage() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();
        
        if (!message || this.isTyping) return;
        
        if (!this.activeAgentId) {
            this.showError('Please select an agent first');
            return;
        }

        // Clear input and add user message
        input.value = '';
        this.addMessageToChat(message, 'user');
        
        // Show typing indicator
        this.showTypingIndicator();
        
        try {
            // Send to LLM service
            const response = await fetch(`http://localhost:8000/api/agents/${this.activeAgentId}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message,
                    context: {
                        timestamp: Date.now(),
                        source: 'web_interface'
                    }
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.hideTypingIndicator();
                this.addMessageToChat(data.response, 'ai');
                
                // Store conversation locally
                this.storeConversation(this.activeAgentId, message, data.response);
                
            } else {
                throw new Error('Failed to get response from agent');
            }
            
        } catch (error) {
            console.error('Chat error:', error);
            this.hideTypingIndicator();
            this.addMessageToChat(
                "I'm sorry, I'm experiencing technical difficulties. Please try again.", 
                'ai'
            );
        }
    }

    sendSuggestion(suggestion) {
        const input = document.getElementById('chat-input');
        input.value = suggestion;
        this.sendMessage();
    }

    // Advanced AI Model Integration
    async sendGeminiRequest(prompt, context = {}) {
        try {
            const response = await fetch('http://localhost:8003/api/ai/gemini/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt, context })
            });

            if (response.ok) {
                const data = await response.json();
                this.addMessageToChat(data.response, 'ai');
                return data;
            } else {
                throw new Error('Gemini service unavailable');
            }
        } catch (error) {
            this.addMessageToChat('Gemini AI temporarily unavailable. Using fallback response.', 'ai');
            return null;
        }
    }

    async generateImage(prompt, style = 'realistic') {
        try {
            this.addMessageToChat(`🎨 Generating image: "${prompt}"...`, 'ai');

            const response = await fetch('http://localhost:8003/api/ai/dalle/generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt, style })
            });

            if (response.ok) {
                const data = await response.json();
                this.addImageToChat(data.image_url, prompt, 'DALL-E 3');
                return data;
            } else {
                throw new Error('DALL-E service unavailable');
            }
        } catch (error) {
            this.addMessageToChat('❌ Image generation failed. Please try again.', 'ai');
            return null;
        }
    }

    async generateStableDiffusionImage(prompt, style_preset = 'enhance') {
        try {
            this.addMessageToChat(`🎨 Creating Stable Diffusion image: "${prompt}"...`, 'ai');

            const response = await fetch('http://localhost:8003/api/ai/stable-diffusion/generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt, style_preset })
            });

            if (response.ok) {
                const data = await response.json();
                this.addImageToChat(data.image_url, prompt, 'Stable Diffusion XL');
                return data;
            } else {
                throw new Error('Stable Diffusion service unavailable');
            }
        } catch (error) {
            this.addMessageToChat('❌ Stable Diffusion generation failed. Please try again.', 'ai');
            return null;
        }
    }

    async generateVideo(prompt, duration = 10) {
        try {
            this.addMessageToChat(`🎬 Generating video with Sora: "${prompt}"...`, 'ai');

            const response = await fetch('http://localhost:8003/api/ai/sora/generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt, duration })
            });

            if (response.ok) {
                const data = await response.json();
                this.addVideoToChat(data.video_url, prompt, 'OpenAI Sora');
                return data;
            } else {
                throw new Error('Sora service unavailable');
            }
        } catch (error) {
            this.addMessageToChat('❌ Video generation failed. Please try again.', 'ai');
            return null;
        }
    }

    async makeTensorFlowPrediction(model, inputData) {
        try {
            const response = await fetch('http://localhost:8003/api/ai/tensorflow/predict', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ model, input_data: inputData })
            });

            if (response.ok) {
                const data = await response.json();
                this.addMessageToChat(`🧠 TensorFlow ${model} prediction: ${JSON.stringify(data.prediction)}`, 'ai');
                return data;
            } else {
                throw new Error('TensorFlow service unavailable');
            }
        } catch (error) {
            this.addMessageToChat('❌ TensorFlow prediction failed. Please try again.', 'ai');
            return null;
        }
    }

    addImageToChat(imageUrl, prompt, model) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message image-message';

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="image-container">
                    <img src="${imageUrl}" alt="${prompt}" class="generated-image" />
                    <div class="image-info">
                        <span class="image-model">${model}</span>
                        <span class="image-prompt">"${prompt}"</span>
                    </div>
                </div>
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    addVideoToChat(videoUrl, prompt, model) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message video-message';

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="video-container">
                    <div class="video-placeholder">
                        <span class="video-icon">🎬</span>
                        <div class="video-info">
                            <span class="video-model">${model}</span>
                            <span class="video-prompt">"${prompt}"</span>
                            <span class="video-status">Video generated successfully</span>
                        </div>
                    </div>
                </div>
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    addMessageToChat(message, sender, animate = true) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        if (animate) {
            messageDiv.classList.add('message-enter');
        }

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageDiv.innerHTML = `
            <div class="message-content">
                ${this.formatMessage(message)}
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Remove animation class after animation
        if (animate) {
            setTimeout(() => messageDiv.classList.remove('message-enter'), 300);
        }
    }

    formatMessage(message) {
        // Convert markdown-like formatting to HTML
        let formatted = message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');

        // Format lists
        formatted = formatted.replace(/^• (.+)$/gm, '<li>$1</li>');
        if (formatted.includes('<li>')) {
            formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
        }

        // Format price mentions
        formatted = formatted.replace(/\$([0-9,]+\.?[0-9]*)/g, '<span class="price">$$$1</span>');
        
        // Format percentages
        formatted = formatted.replace(/([+-]?[0-9]+\.?[0-9]*%)/g, '<span class="percentage">$1</span>');

        return formatted;
    }

    showTypingIndicator() {
        this.isTyping = true;
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-indicator';
        typingDiv.id = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;

        const chatMessages = document.getElementById('chat-messages');
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    hideTypingIndicator() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    storeConversation(agentId, userMessage, aiResponse) {
        const conversations = this.conversations.get(agentId) || [];
        conversations.push({
            timestamp: Date.now(),
            user_message: userMessage,
            ai_response: aiResponse
        });
        this.conversations.set(agentId, conversations);
    }

    minimizeChat() {
        const chatContainer = document.getElementById('llm-chat-container');
        chatContainer.classList.toggle('minimized');
    }

    closeChat() {
        const chatContainer = document.getElementById('llm-chat-container');
        chatContainer.classList.add('hidden');
        this.activeAgentId = null;
    }

    showError(message) {
        this.addMessageToChat(`❌ Error: ${message}`, 'ai');
    }

    // Market analysis shortcuts
    async analyzeMarket(pair = 'BTC/USDT') {
        if (!this.activeAgentId) {
            this.showError('Please select an agent first');
            return;
        }

        const message = `Provide a comprehensive analysis of ${pair} including current price trends, technical indicators, and trading recommendations.`;
        document.getElementById('chat-input').value = message;
        await this.sendMessage();
    }

    async getArbitrageOpportunities() {
        if (!this.activeAgentId) {
            this.showError('Please select an agent first');
            return;
        }

        const message = "Show me current arbitrage opportunities across different exchanges with profit potential and risk assessment.";
        document.getElementById('chat-input').value = message;
        await this.sendMessage();
    }

    async optimizePortfolio() {
        if (!this.activeAgentId) {
            this.showError('Please select an agent first');
            return;
        }

        const message = "Analyze my current portfolio and provide optimization recommendations for better risk-adjusted returns.";
        document.getElementById('chat-input').value = message;
        await this.sendMessage();
    }
}

// Global Professional AI Assistant instance
let professionalAI;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    professionalAI = new ProfessionalAIAssistant();
    window.professionalAI = professionalAI;

    // Backward compatibility
    window.llmChat = professionalAI;
});

// Global functions for easy access
function openAgentChat(agentId) {
    if (llmChat) {
        llmChat.openAgentChat(agentId);
    }
}

function analyzeMarket(pair) {
    if (llmChat) {
        llmChat.analyzeMarket(pair);
    }
}

function getArbitrageOpportunities() {
    if (llmChat) {
        llmChat.getArbitrageOpportunities();
    }
}

function optimizePortfolio() {
    if (llmChat) {
        llmChat.optimizePortfolio();
    }
}
