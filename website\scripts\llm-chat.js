// LLM Chat Integration for Kontour Coin AI Agents
class LLMChatManager {
    constructor() {
        this.conversations = new Map();
        this.activeAgentId = null;
        this.isTyping = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeChatInterface();
    }

    setupEventListeners() {
        // Chat input handling
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        // Send button
        const sendButton = document.getElementById('send-message-btn');
        if (sendButton) {
            sendButton.addEventListener('click', () => this.sendMessage());
        }

        // Agent selection
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('agent-chat-btn')) {
                const agentId = e.target.dataset.agentId;
                this.openAgentChat(agentId);
            }
        });
    }

    initializeChatInterface() {
        // Create chat interface if it doesn't exist
        if (!document.getElementById('llm-chat-container')) {
            this.createChatInterface();
        }
    }

    createChatInterface() {
        const chatHTML = `
            <div id="llm-chat-container" class="chat-container hidden">
                <div class="chat-header">
                    <div class="chat-agent-info">
                        <div class="agent-avatar">🤖</div>
                        <div class="agent-details">
                            <h4 id="chat-agent-name">AI Agent</h4>
                            <span id="chat-agent-status">Online</span>
                        </div>
                    </div>
                    <div class="chat-controls">
                        <button class="btn btn-sm btn-outline" onclick="llmChat.minimizeChat()">−</button>
                        <button class="btn btn-sm btn-outline" onclick="llmChat.closeChat()">×</button>
                    </div>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <div class="welcome-message">
                        <div class="message ai-message">
                            <div class="message-content">
                                <p>👋 Hello! I'm your AI trading assistant. I can help you with:</p>
                                <ul>
                                    <li>📊 Market analysis and insights</li>
                                    <li>💰 Trading recommendations</li>
                                    <li>📈 Portfolio optimization</li>
                                    <li>⚠️ Risk assessment</li>
                                    <li>🔍 Arbitrage opportunities</li>
                                </ul>
                                <p>What would you like to know?</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="chat-input-container">
                    <div class="chat-suggestions" id="chat-suggestions">
                        <button class="suggestion-btn" onclick="llmChat.sendSuggestion('Analyze BTC market conditions')">
                            📊 Analyze BTC
                        </button>
                        <button class="suggestion-btn" onclick="llmChat.sendSuggestion('Show arbitrage opportunities')">
                            🔍 Arbitrage
                        </button>
                        <button class="suggestion-btn" onclick="llmChat.sendSuggestion('Optimize my portfolio')">
                            📈 Optimize
                        </button>
                    </div>
                    
                    <div class="chat-input-wrapper">
                        <textarea 
                            id="chat-input" 
                            placeholder="Ask me anything about trading, markets, or crypto..."
                            rows="1"
                        ></textarea>
                        <button id="send-message-btn" class="send-btn">
                            <span class="send-icon">➤</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', chatHTML);
    }

    async openAgentChat(agentId) {
        try {
            this.activeAgentId = agentId;
            
            // Get agent details
            const agent = window.workflowManager?.aiAgents.get(agentId);
            if (agent) {
                document.getElementById('chat-agent-name').textContent = agent.name;
                document.getElementById('chat-agent-status').textContent = agent.status;
            }

            // Load conversation history
            await this.loadConversationHistory(agentId);
            
            // Show chat interface
            const chatContainer = document.getElementById('llm-chat-container');
            chatContainer.classList.remove('hidden');
            
            // Focus input
            document.getElementById('chat-input').focus();
            
        } catch (error) {
            console.error('Error opening agent chat:', error);
            this.showError('Failed to open chat with agent');
        }
    }

    async loadConversationHistory(agentId) {
        try {
            const response = await fetch(`http://localhost:8001/api/llm/conversations/${agentId}`);
            if (response.ok) {
                const data = await response.json();
                this.displayConversationHistory(data.messages || []);
            }
        } catch (error) {
            console.log('No previous conversation history found');
        }
    }

    displayConversationHistory(messages) {
        const chatMessages = document.getElementById('chat-messages');
        const welcomeMessage = chatMessages.querySelector('.welcome-message');
        
        if (messages.length > 0) {
            // Hide welcome message if there's history
            if (welcomeMessage) welcomeMessage.style.display = 'none';
            
            // Display last 10 messages
            const recentMessages = messages.slice(-10);
            recentMessages.forEach(msg => {
                this.addMessageToChat(msg.user_message, 'user', false);
                this.addMessageToChat(msg.ai_response, 'ai', false);
            });
        }
    }

    async sendMessage() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();
        
        if (!message || this.isTyping) return;
        
        if (!this.activeAgentId) {
            this.showError('Please select an agent first');
            return;
        }

        // Clear input and add user message
        input.value = '';
        this.addMessageToChat(message, 'user');
        
        // Show typing indicator
        this.showTypingIndicator();
        
        try {
            // Send to LLM service
            const response = await fetch(`http://localhost:8000/api/agents/${this.activeAgentId}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message,
                    context: {
                        timestamp: Date.now(),
                        source: 'web_interface'
                    }
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.hideTypingIndicator();
                this.addMessageToChat(data.response, 'ai');
                
                // Store conversation locally
                this.storeConversation(this.activeAgentId, message, data.response);
                
            } else {
                throw new Error('Failed to get response from agent');
            }
            
        } catch (error) {
            console.error('Chat error:', error);
            this.hideTypingIndicator();
            this.addMessageToChat(
                "I'm sorry, I'm experiencing technical difficulties. Please try again.", 
                'ai'
            );
        }
    }

    sendSuggestion(suggestion) {
        const input = document.getElementById('chat-input');
        input.value = suggestion;
        this.sendMessage();
    }

    addMessageToChat(message, sender, animate = true) {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        if (animate) {
            messageDiv.classList.add('message-enter');
        }

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageDiv.innerHTML = `
            <div class="message-content">
                ${this.formatMessage(message)}
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Remove animation class after animation
        if (animate) {
            setTimeout(() => messageDiv.classList.remove('message-enter'), 300);
        }
    }

    formatMessage(message) {
        // Convert markdown-like formatting to HTML
        let formatted = message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');

        // Format lists
        formatted = formatted.replace(/^• (.+)$/gm, '<li>$1</li>');
        if (formatted.includes('<li>')) {
            formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
        }

        // Format price mentions
        formatted = formatted.replace(/\$([0-9,]+\.?[0-9]*)/g, '<span class="price">$$$1</span>');
        
        // Format percentages
        formatted = formatted.replace(/([+-]?[0-9]+\.?[0-9]*%)/g, '<span class="percentage">$1</span>');

        return formatted;
    }

    showTypingIndicator() {
        this.isTyping = true;
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-indicator';
        typingDiv.id = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;

        const chatMessages = document.getElementById('chat-messages');
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    hideTypingIndicator() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    storeConversation(agentId, userMessage, aiResponse) {
        const conversations = this.conversations.get(agentId) || [];
        conversations.push({
            timestamp: Date.now(),
            user_message: userMessage,
            ai_response: aiResponse
        });
        this.conversations.set(agentId, conversations);
    }

    minimizeChat() {
        const chatContainer = document.getElementById('llm-chat-container');
        chatContainer.classList.toggle('minimized');
    }

    closeChat() {
        const chatContainer = document.getElementById('llm-chat-container');
        chatContainer.classList.add('hidden');
        this.activeAgentId = null;
    }

    showError(message) {
        this.addMessageToChat(`❌ Error: ${message}`, 'ai');
    }

    // Market analysis shortcuts
    async analyzeMarket(pair = 'BTC/USDT') {
        if (!this.activeAgentId) {
            this.showError('Please select an agent first');
            return;
        }

        const message = `Provide a comprehensive analysis of ${pair} including current price trends, technical indicators, and trading recommendations.`;
        document.getElementById('chat-input').value = message;
        await this.sendMessage();
    }

    async getArbitrageOpportunities() {
        if (!this.activeAgentId) {
            this.showError('Please select an agent first');
            return;
        }

        const message = "Show me current arbitrage opportunities across different exchanges with profit potential and risk assessment.";
        document.getElementById('chat-input').value = message;
        await this.sendMessage();
    }

    async optimizePortfolio() {
        if (!this.activeAgentId) {
            this.showError('Please select an agent first');
            return;
        }

        const message = "Analyze my current portfolio and provide optimization recommendations for better risk-adjusted returns.";
        document.getElementById('chat-input').value = message;
        await this.sendMessage();
    }
}

// Global LLM Chat instance
let llmChat;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    llmChat = new LLMChatManager();
    window.llmChat = llmChat;
});

// Global functions for easy access
function openAgentChat(agentId) {
    if (llmChat) {
        llmChat.openAgentChat(agentId);
    }
}

function analyzeMarket(pair) {
    if (llmChat) {
        llmChat.analyzeMarket(pair);
    }
}

function getArbitrageOpportunities() {
    if (llmChat) {
        llmChat.getArbitrageOpportunities();
    }
}

function optimizePortfolio() {
    if (llmChat) {
        llmChat.optimizePortfolio();
    }
}
