{"name": "kontour-coin-backend", "version": "1.0.0", "description": "Kontour Coin Official Website Backend with AI APIs and Silicon Valley data integration", "main": "kontour-server.js", "scripts": {"start": "node ai-enhanced-server.js", "dev": "nodemon ai-enhanced-server.js", "legacy": "node kontour-server.js", "test": "jest", "lint": "eslint .", "build": "npm run lint && npm test", "docker:build": "docker build -t kontour-coin-backend .", "docker:run": "docker run -p 3001:3001 kontour-coin-backend", "start:all": "concurrently \"npm run start\" \"npm run start:ai-workflow\"", "start:ai-workflow": "cd ../services/ai-workflow-orchestrator && python ai_workflow_orchestrator.py"}, "keywords": ["kontour-coin", "cryptocurrency", "blockchain", "ai", "quantum-computing", "silicon-valley", "defi", "web3"], "author": "Kontour Coin Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0", "axios": "^1.6.2", "ws": "^8.14.2", "socket.io": "^4.7.4", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@types/node": "^20.10.4", "typescript": "^5.3.3", "ts-node": "^10.9.1", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/kontour-coin/backend.git"}, "bugs": {"url": "https://github.com/kontour-coin/backend/issues"}, "homepage": "https://kontourcoin.com", "jest": {"testEnvironment": "node", "collectCoverageFrom": ["**/*.js", "!node_modules/**", "!coverage/**"], "testMatch": ["**/__tests__/**/*.js", "**/?(*.)+(spec|test).js"]}, "eslintConfig": {"extends": ["airbnb-base"], "env": {"node": true, "es6": true, "jest": true}, "rules": {"no-console": "off", "consistent-return": "off", "func-names": "off", "object-shorthand": "off", "no-process-exit": "off", "no-param-reassign": "off", "no-return-await": "off", "no-underscore-dangle": "off", "class-methods-use-this": "off", "prefer-destructuring": ["error", {"object": true, "array": false}]}}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "nodemonConfig": {"ignore": ["node_modules/*", "public/*"], "delay": "1000"}}