{"name": "kontour-exchange-integration", "version": "1.0.0", "description": "Multi-Exchange Integration Service for Kontour Coin Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["exchange", "cryptocurrency", "trading", "arbitrage", "kontour", "binance", "coinbase", "kraken"], "author": "Kontour Coin Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "node-fetch": "^3.3.2", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "ccxt": "^4.1.30", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}