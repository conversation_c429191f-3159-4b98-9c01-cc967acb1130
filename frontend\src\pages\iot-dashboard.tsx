import React, { useState, useEffect } from 'react';
// import * as ethers from 'ethers'; // Disabled for development
import { Line } from 'react-chartjs-2';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  Title, 
  Tooltip, 
  Legend 
} from 'chart.js';
import axios from 'axios';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

// Contract ABI (partial, just what we need)
const contractABI = [
  "event ReadingRecorded(string deviceId, uint256 timestamp, int256 temperature, int256 zScore, string aiComment)",
  "function recordReading(string memory deviceId, uint256 timestamp, int256 temperature, int256 zScore, string memory aiComment) external",
  "function getReading(string memory deviceId, uint256 timestamp) external view returns (int256 temperature, int256 zScore, string memory aiComment)"
];

const IoTDashboardPage: React.FC = () => {
  const [devices, setDevices] = useState<string[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>('');
  const [readings, setReadings] = useState<any[]>([]);
  const [quantumReadings, setQuantumReadings] = useState<any[]>([]);
  const [blockchainEvents, setBlockchainEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [chartData, setChartData] = useState<any>(null);
  const [anomalyChartData, setAnomalyChartData] = useState<any>(null);
  const [aiInsights, setAiInsights] = useState<string[]>([]);

  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
  const CONTRACT_ADDRESS = process.env.REACT_APP_CONTRACT_ADDRESS || '';
  
  // Fetch list of devices
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const response = await axios.get(`${API_URL}/devices`);
        setDevices(response.data);
        if (response.data.length > 0) {
          setSelectedDevice(response.data[0]);
        }
      } catch (err) {
        console.error('Error fetching devices:', err);
        setError('Failed to fetch devices');
      }
    };
    
    fetchDevices();
  }, [API_URL]);
  
  // Fetch data when device is selected
  useEffect(() => {
    if (!selectedDevice) return;
    
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Fetch regular readings
        const readingsResponse = await axios.get(`${API_URL}/readings`, {
          params: { deviceId: selectedDevice, limit: 50 }
        });
        setReadings(readingsResponse.data);
        
        // Fetch quantum-enhanced readings
        const quantumResponse = await axios.get(`${API_URL}/quantum-readings`, {
          params: { deviceId: selectedDevice, limit: 50 }
        });
        setQuantumReadings(quantumResponse.data);
        
        // Fetch blockchain events
        const eventsResponse = await axios.get(`${API_URL}/blockchain-events`, {
          params: { deviceId: selectedDevice }
        });
        setBlockchainEvents(eventsResponse.data);
        
        // Extract AI insights
        const insights = readingsResponse.data
          .filter((reading: any) => reading.aiComment)
          .map((reading: any) => reading.aiComment);
        setAiInsights(insights);
        
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
    
    // Set up interval to refresh data
    const intervalId = setInterval(fetchData, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(intervalId);
  }, [selectedDevice, API_URL]);
  
  // Prepare chart data
  useEffect(() => {
    if (readings.length === 0) return;
    
    // Sort readings by timestamp
    const sortedReadings = [...readings].sort((a, b) => a.timestamp - b.timestamp);
    
    // Prepare temperature chart data
    const labels = sortedReadings.map(reading => {
      const date = new Date(reading.timestamp * 1000);
      return date.toLocaleTimeString();
    });
    
    const temperatureData = sortedReadings.map(reading => reading.temperature);
    
    setChartData({
      labels,
      datasets: [
        {
          label: 'Temperature',
          data: temperatureData,
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          tension: 0.1
        }
      ]
    });
    
    // Prepare anomaly score chart data
    const anomalyScores = sortedReadings
      .filter(reading => reading.anomalyZ !== undefined)
      .map(reading => reading.anomalyZ);
    
    const anomalyLabels = sortedReadings
      .filter(reading => reading.anomalyZ !== undefined)
      .map(reading => {
        const date = new Date(reading.timestamp * 1000);
        return date.toLocaleTimeString();
      });
    
    // Add quantum scores if available
    const quantumScores = quantumReadings
      .filter(reading => reading.quantum_anomaly_score !== undefined)
      .map(reading => reading.quantum_anomaly_score);
    
    const quantumLabels = quantumReadings
      .filter(reading => reading.quantum_anomaly_score !== undefined)
      .map(reading => {
        const date = new Date(reading.timestamp * 1000);
        return date.toLocaleTimeString();
      });
    
    setAnomalyChartData({
      labels: [...anomalyLabels, ...quantumLabels],
      datasets: [
        {
          label: 'Classical Anomaly Score',
          data: anomalyScores,
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          tension: 0.1
        },
        {
          label: 'Quantum Anomaly Score',
          data: [...Array(anomalyLabels.length).fill(null), ...quantumScores],
          borderColor: 'rgb(54, 162, 235)',
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          tension: 0.1
        }
      ]
    });
    
  }, [readings, quantumReadings]);
  
  // Listen for new blockchain events
  useEffect(() => {
    // Blockchain integration disabled for development
    // TODO: Implement proper ethers.js integration when needed
    console.log('Blockchain integration placeholder for device:', selectedDevice);
  }, [selectedDevice, CONTRACT_ADDRESS]);
  
  return (
    <div className="iot-dashboard">
      <h1>IoT Sensor Dashboard</h1>
      
      {error && <div className="error-message">{error}</div>}
      
      <div className="device-selector">
        <label htmlFor="device-select">Select Device:</label>
        <select 
          id="device-select"
          value={selectedDevice}
          onChange={(e) => setSelectedDevice(e.target.value)}
          disabled={loading}
        >
          <option value="">Select a device</option>
          {devices.map(device => (
            <option key={device} value={device}>{device}</option>
          ))}
        </select>
      </div>
      
      {loading && <div className="loading">Loading data...</div>}
      
      <div className="dashboard-grid">
        {/* Temperature Chart */}
        <div className="chart-container">
          <h2>Temperature Readings</h2>
          {chartData && <Line data={chartData} options={{ responsive: true }} />}
        </div>
        
        {/* Anomaly Score Chart */}
        <div className="chart-container">
          <h2>Anomaly Detection Scores</h2>
          {anomalyChartData && <Line data={anomalyChartData} options={{ responsive: true }} />}
        </div>
        
        {/* AI Insights */}
        <div className="ai-insights">
          <h2>AI Insights</h2>
          {aiInsights.length > 0 ? (
            <ul>
              {aiInsights.slice(0, 5).map((insight, index) => (
                <li key={index}>{insight}</li>
              ))}
            </ul>
          ) : (
            <p>No AI insights available</p>
          )}
        </div>
        
        {/* Blockchain Events */}
        <div className="blockchain-events">
          <h2>Blockchain Records</h2>
          {blockchainEvents.length > 0 ? (
            <table>
              <thead>
                <tr>
                  <th>Time</th>
                  <th>Temperature</th>
                  <th>Anomaly Score</th>
                </tr>
              </thead>
              <tbody>
                {blockchainEvents.slice(0, 5).map((event, index) => (
                  <tr key={index}>
                    <td>{new Date(event.timestamp * 1000).toLocaleString()}</td>
                    <td>{typeof event.temperature === 'number' ? event.temperature.toFixed(2) : event.temperature}</td>
                    <td>{typeof event.zScore === 'number' ? event.zScore.toFixed(2) : event.zScore}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p>No blockchain records available</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default IoTDashboardPage;
