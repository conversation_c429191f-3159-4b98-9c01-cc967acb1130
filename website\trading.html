<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>Trading Dashboard | Kontour Coin 💎</title>
    <meta name="description" content="Advanced AI-powered trading dashboard with real-time analytics, automated strategies, and quantum security.">
    <meta name="keywords" content="cryptocurrency trading, AI trading, automated trading, real-time analytics, trading dashboard">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles/advanced-professional.css" as="style">
    <link rel="preload" href="scripts/advanced-main.js" as="script">
    
    <!-- External resources -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link href="styles/advanced-professional.css" rel="stylesheet">
    
    <!-- Favicons -->
    <link rel="icon" type="image/svg+xml" href="assets/kontour-logo.svg">
    
    <style>
        .trading-dashboard {
            padding-top: 80px;
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        .dashboard-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: var(--space-6) 0;
            margin-bottom: var(--space-8);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }
        
        .trading-chart {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--gray-200);
        }
        
        .trading-sidebar {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }
        
        .price-widget {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
        }
        
        .price-display {
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            color: var(--success);
            margin-bottom: var(--space-2);
        }
        
        .price-change {
            font-size: var(--text-lg);
            font-weight: var(--font-medium);
        }
        
        .price-change.positive {
            color: var(--success);
        }
        
        .price-change.negative {
            color: var(--error);
        }
        
        .trading-controls {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-200);
        }
        
        .trade-tabs {
            display: flex;
            margin-bottom: var(--space-6);
            border-bottom: 1px solid var(--gray-200);
        }
        
        .trade-tab {
            flex: 1;
            padding: var(--space-3) var(--space-4);
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: var(--font-medium);
            color: var(--gray-600);
            transition: var(--transition-fast);
        }
        
        .trade-tab.active {
            color: var(--primary-purple);
            border-bottom: 2px solid var(--primary-purple);
        }
        
        .form-group {
            margin-bottom: var(--space-4);
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--space-2);
            font-weight: var(--font-medium);
            color: var(--gray-700);
        }
        
        .form-input {
            width: 100%;
            padding: var(--space-3);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            font-size: var(--text-base);
            transition: var(--transition-fast);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-purple);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .ai-insights {
            background: var(--gradient-primary);
            color: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
        }
        
        .insight-item {
            display: flex;
            align-items: center;
            margin-bottom: var(--space-3);
        }
        
        .insight-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--space-3);
        }
        
        .portfolio-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }
        
        .portfolio-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            text-align: center;
            border: 1px solid var(--gray-200);
        }
        
        .portfolio-value {
            font-size: var(--text-2xl);
            font-weight: var(--font-bold);
            color: var(--gray-900);
            margin-bottom: var(--space-2);
        }
        
        .portfolio-label {
            color: var(--gray-600);
            font-size: var(--text-sm);
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .portfolio-summary {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="navbar-container">
                <a href="index-new.html" class="navbar-brand">
                    <img src="assets/kontour-logo.svg" alt="Kontour Coin" class="navbar-logo">
                    <span>Kontour Coin</span>
                </a>
                
                <ul class="navbar-nav">
                    <li><a href="index-new.html">Home</a></li>
                    <li><a href="trading.html" class="active">Trading</a></li>
                    <li><a href="wallet.html">Wallet</a></li>
                    <li><a href="analytics.html">Analytics</a></li>
                    <li><a href="ai-agents.html">AI Agents</a></li>
                </ul>
                
                <div class="navbar-actions">
                    <a href="#wallet" class="btn btn-outline btn-sm">Connect Wallet</a>
                    <a href="#profile" class="btn btn-primary btn-sm">Profile</a>
                </div>
                
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Trading Dashboard -->
    <div class="trading-dashboard">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div class="container">
                <h1>AI-Powered Trading Dashboard</h1>
                <p>Advanced cryptocurrency trading with autonomous AI agents and real-time analytics</p>
            </div>
        </div>

        <div class="container">
            <!-- Portfolio Summary -->
            <div class="portfolio-summary">
                <div class="portfolio-card">
                    <div class="portfolio-value" id="totalBalance">$0.00</div>
                    <div class="portfolio-label">Total Balance</div>
                </div>
                <div class="portfolio-card">
                    <div class="portfolio-value" id="totalPnL">+$0.00</div>
                    <div class="portfolio-label">24h P&L</div>
                </div>
                <div class="portfolio-card">
                    <div class="portfolio-value" id="activePositions">0</div>
                    <div class="portfolio-label">Active Positions</div>
                </div>
                <div class="portfolio-card">
                    <div class="portfolio-value" id="aiAgents">3</div>
                    <div class="portfolio-label">AI Agents Active</div>
                </div>
            </div>

            <!-- Main Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Trading Chart -->
                <div class="trading-chart">
                    <div style="text-align: center; color: var(--gray-500);">
                        <i class="fas fa-chart-line" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <h3>Advanced Trading Chart</h3>
                        <p>Real-time price data with AI-powered technical analysis</p>
                        <div style="margin-top: 2rem;">
                            <div class="btn btn-primary">
                                <i class="fas fa-play"></i>
                                Launch TradingView
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trading Sidebar -->
                <div class="trading-sidebar">
                    <!-- Price Widget -->
                    <div class="price-widget">
                        <h3 style="margin-bottom: 1rem;">KONT/USDT</h3>
                        <div class="price-display" id="currentPrice">$2.4567</div>
                        <div class="price-change positive" id="priceChangePercent">+5.23% (+$0.1234)</div>
                        
                        <div style="margin-top: 1.5rem; display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.875rem;">
                            <div>
                                <div style="color: var(--gray-600);">24h High</div>
                                <div style="font-weight: 600;">$2.5891</div>
                            </div>
                            <div>
                                <div style="color: var(--gray-600);">24h Low</div>
                                <div style="font-weight: 600;">$2.3456</div>
                            </div>
                            <div>
                                <div style="color: var(--gray-600);">Volume</div>
                                <div style="font-weight: 600;">$45.2M</div>
                            </div>
                            <div>
                                <div style="color: var(--gray-600);">Market Cap</div>
                                <div style="font-weight: 600;">$2.4B</div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Insights -->
                    <div class="ai-insights">
                        <h4 style="margin-bottom: 1rem; display: flex; align-items: center;">
                            <i class="fas fa-robot" style="margin-right: 0.5rem;"></i>
                            AI Market Insights
                        </h4>
                        
                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-trending-up"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 0.25rem;">Bullish Signal</div>
                                <div style="font-size: 0.875rem; opacity: 0.9;">Strong upward momentum detected</div>
                            </div>
                        </div>
                        
                        <div class="insight-item">
                            <div class="insight-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 0.25rem;">Risk Level: Low</div>
                                <div style="font-size: 0.875rem; opacity: 0.9;">Optimal conditions for trading</div>
                            </div>
                        </div>
                        
                        <div class="insight-item" style="margin-bottom: 0;">
                            <div class="insight-icon">
                                <i class="fas fa-target"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 0.25rem;">Target: $2.75</div>
                                <div style="font-size: 0.875rem; opacity: 0.9;">AI prediction for next 24h</div>
                            </div>
                        </div>
                    </div>

                    <!-- Trading Controls -->
                    <div class="trading-controls">
                        <div class="trade-tabs">
                            <button class="trade-tab active" data-tab="buy">Buy</button>
                            <button class="trade-tab" data-tab="sell">Sell</button>
                        </div>
                        
                        <div class="trade-content" id="buyTab">
                            <div class="form-group">
                                <label class="form-label">Amount (USDT)</label>
                                <input type="number" class="form-input" placeholder="0.00" id="buyAmount">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Price (USDT)</label>
                                <input type="number" class="form-input" placeholder="Market Price" id="buyPrice">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Order Type</label>
                                <select class="form-input" id="buyOrderType">
                                    <option value="market">Market Order</option>
                                    <option value="limit">Limit Order</option>
                                    <option value="stop">Stop Order</option>
                                    <option value="ai">AI Optimized</option>
                                </select>
                            </div>
                            
                            <button class="btn btn-primary" style="width: 100%; margin-top: 1rem;">
                                <i class="fas fa-shopping-cart"></i>
                                Place Buy Order
                            </button>
                        </div>
                        
                        <div class="trade-content" id="sellTab" style="display: none;">
                            <div class="form-group">
                                <label class="form-label">Amount (KONT)</label>
                                <input type="number" class="form-input" placeholder="0.00" id="sellAmount">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Price (USDT)</label>
                                <input type="number" class="form-input" placeholder="Market Price" id="sellPrice">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Order Type</label>
                                <select class="form-input" id="sellOrderType">
                                    <option value="market">Market Order</option>
                                    <option value="limit">Limit Order</option>
                                    <option value="stop">Stop Order</option>
                                    <option value="ai">AI Optimized</option>
                                </select>
                            </div>
                            
                            <button class="btn" style="width: 100%; margin-top: 1rem; background: var(--error); color: white;">
                                <i class="fas fa-hand-holding-usd"></i>
                                Place Sell Order
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Trades -->
            <div class="card" style="margin-bottom: 2rem;">
                <div class="card-header">
                    <h3>Recent Trades</h3>
                </div>
                <div class="card-body">
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="border-bottom: 1px solid var(--gray-200);">
                                    <th style="text-align: left; padding: 1rem; color: var(--gray-600);">Time</th>
                                    <th style="text-align: left; padding: 1rem; color: var(--gray-600);">Pair</th>
                                    <th style="text-align: left; padding: 1rem; color: var(--gray-600);">Type</th>
                                    <th style="text-align: right; padding: 1rem; color: var(--gray-600);">Amount</th>
                                    <th style="text-align: right; padding: 1rem; color: var(--gray-600);">Price</th>
                                    <th style="text-align: right; padding: 1rem; color: var(--gray-600);">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid var(--gray-100);">
                                    <td style="padding: 1rem;">14:32:15</td>
                                    <td style="padding: 1rem;">KONT/USDT</td>
                                    <td style="padding: 1rem;"><span style="color: var(--success);">Buy</span></td>
                                    <td style="padding: 1rem; text-align: right;">1,000 KONT</td>
                                    <td style="padding: 1rem; text-align: right;">$2.4567</td>
                                    <td style="padding: 1rem; text-align: right;"><span style="color: var(--success); font-size: 0.875rem;">Filled</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid var(--gray-100);">
                                    <td style="padding: 1rem;">14:28:42</td>
                                    <td style="padding: 1rem;">KONT/USDT</td>
                                    <td style="padding: 1rem;"><span style="color: var(--error);">Sell</span></td>
                                    <td style="padding: 1rem; text-align: right;">500 KONT</td>
                                    <td style="padding: 1rem; text-align: right;">$2.4123</td>
                                    <td style="padding: 1rem; text-align: right;"><span style="color: var(--success); font-size: 0.875rem;">Filled</span></td>
                                </tr>
                                <tr>
                                    <td style="padding: 1rem;">14:15:33</td>
                                    <td style="padding: 1rem;">KONT/USDT</td>
                                    <td style="padding: 1rem;"><span style="color: var(--success);">Buy</span></td>
                                    <td style="padding: 1rem; text-align: right;">2,500 KONT</td>
                                    <td style="padding: 1rem; text-align: right;">$2.3891</td>
                                    <td style="padding: 1rem; text-align: right;"><span style="color: var(--warning); font-size: 0.875rem;">Partial</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="scripts/advanced-main.js"></script>
    <script>
        // Initialize trading dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            initializeTradingDashboard();
        });

        function initializeTradingDashboard() {
            // Initialize trade tabs
            const tradeTabs = document.querySelectorAll('.trade-tab');
            tradeTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabName = tab.dataset.tab;
                    switchTradeTab(tabName);
                });
            });

            // Update portfolio values
            updatePortfolioValues();
            
            // Start real-time updates
            setInterval(updateTradingData, 5000);
        }

        function switchTradeTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.trade-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // Update tab content
            document.getElementById('buyTab').style.display = tabName === 'buy' ? 'block' : 'none';
            document.getElementById('sellTab').style.display = tabName === 'sell' ? 'block' : 'none';
        }

        function updatePortfolioValues() {
            document.getElementById('totalBalance').textContent = '$125,432.67';
            document.getElementById('totalPnL').textContent = '+$5,234.12';
            document.getElementById('activePositions').textContent = '7';
        }

        function updateTradingData() {
            // Simulate real-time price updates
            const price = 2.4567 + (Math.random() - 0.5) * 0.1;
            const change = (Math.random() - 0.5) * 10;
            
            document.getElementById('currentPrice').textContent = `$${price.toFixed(4)}`;
            
            const changeElement = document.getElementById('priceChangePercent');
            const isPositive = change >= 0;
            changeElement.textContent = `${isPositive ? '+' : ''}${change.toFixed(2)}% (${isPositive ? '+' : ''}$${Math.abs(change * 0.05).toFixed(4)})`;
            changeElement.className = `price-change ${isPositive ? 'positive' : 'negative'}`;
        }
    </script>
</body>
</html>
